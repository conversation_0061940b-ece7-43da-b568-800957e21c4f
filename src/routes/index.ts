import { describeRoute } from "hono-openapi";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { jsonContent } from "stoker/openapi/helpers";
import { createMessageObjectSchema } from "stoker/openapi/schemas";

import { API_TAGS } from "@/lib/constants";
import { createRouter } from "@/lib/helpers";

const router = createRouter().get(
  "/",
  describeRoute({
    title: "Health Check",
    tags: [API_TAGS.SYSTEM],
    responses: {
      [HttpStatusCodes.OK]: jsonContent(
        createMessageObjectSchema("Group Working CRM API"),
        "Group Working CRM API Index",
      ),
    },
  }),
  (c) => {
    return c.json(
      {
        ok: true,
        message: "Group Working CRM API",
      },
      HttpStatusCodes.OK,
    );
  },
);

export default router;

import db from "@/db";
import { translation, translationKey } from "@/db/entities/translation.entity";
import type * as schema from "@/db/schema";
import type { SupportedLanguage } from "@/lib/constants";
import { logger } from "@/middlewares/pino-logger.middleware";
import { and, eq } from "drizzle-orm";
import type { Relations } from "drizzle-orm";
import type { PgTransaction } from "drizzle-orm/pg-core";
import type { PostgresJsQueryResultHKT } from "drizzle-orm/postgres-js";

type DbTransaction = PgTransaction<
  PostgresJsQueryResultHKT,
  typeof schema,
  Relations<typeof schema>
>;

export class TranslationService {
  static async createText(defaultText: string, tx?: DbTransaction) {
    const queryBuilder = tx || db;
    const [key] = await queryBuilder
      .insert(translationKey)
      .values({ defaultText })
      .returning();
    return key;
  }

  static async addTranslation(
    textId: string,
    language: SupportedLanguage,
    content: string,
    tx?: DbTransaction,
  ) {
    const queryBuilder = tx || db;
    const [result] = await queryBuilder
      .insert(translation)
      .values({ textId, language, content })
      .returning();
    return result;
  }

  static async updateTranslation(
    textId: string,
    language: SupportedLanguage,
    content: string,
    tx?: DbTransaction,
  ) {
    const queryBuilder = tx || db;
    const [result] = await queryBuilder
      .update(translation)
      .set({ content, updatedAt: new Date() })
      .where(
        and(eq(translation.textId, textId), eq(translation.language, language)),
      )
      .returning();
    return result;
  }

  static async getTranslation(textId: string, language: SupportedLanguage) {
    try {
      const translatedText = await db.query.translation.findFirst({
        where: {
          textId,
          language,
        },
      });

      return translatedText;
    } catch (error) {
      logger.error(error, "Failed to get translation");
      return null;
    }
  }

  static async createTextWithTranslations(
    translations: Partial<Record<SupportedLanguage, string>>,
    defaultText = "",
    tx?: DbTransaction,
  ) {
    const queryBuilder = tx || db;
    return await queryBuilder.transaction(async (trx) => {
      const [key] = await trx
        .insert(translationKey)
        .values({ defaultText })
        .returning();

      await Promise.all(
        Object.entries(translations).map(([language, content]) =>
          trx
            .insert(translation)
            .values({
              textId: key.id,
              language: language as SupportedLanguage,
              content,
            })
            .returning(),
        ),
      );

      return key;
    });
  }

  static async deleteText(textId: string, tx?: DbTransaction) {
    const queryBuilder = tx || db;
    await queryBuilder.transaction(async (trx) => {
      await trx.delete(translation).where(eq(translation.textId, textId));
      await trx.delete(translationKey).where(eq(translationKey.id, textId));
    });
  }

  static formatTranslations(translations: schema.Translation[]) {
    return translations.reduce(
      (acc, translation) =>
        Object.assign(acc, {
          [translation.language]: translation.content,
        }),
      {} as Record<SupportedLanguage, string>,
    );
  }
}

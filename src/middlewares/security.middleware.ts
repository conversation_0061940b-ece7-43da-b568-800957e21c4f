import type { AppBindings } from "@/lib/types";
import type { MiddlewareHandler } from "hono";

export const securityHeaders: MiddlewareHandler<AppBindings> = async (
  c,
  next,
) => {
  await next();

  c.header("X-Content-Type-Options", "nosniff");
  c.header("X-Frame-Options", "DENY");
  c.header("X-XSS-Protection", "1; mode=block");
  c.header("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
};

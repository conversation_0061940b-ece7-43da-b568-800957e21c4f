import type { ContentfulStatusCode } from "hono/utils/http-status";

export class APIError extends Error {
  constructor(
    public status: ContentfulStatusCode,
    public body?: {
      message?: string;
      code?: string;
      [key: string]: unknown;
    },
    public headers: HeadersInit = {},
    public statusCode: ContentfulStatusCode = status,
  ) {
    super(body?.message || "API Error");
    this.name = "APIError";
  }
}

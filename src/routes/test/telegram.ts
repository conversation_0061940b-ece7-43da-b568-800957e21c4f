import { describeRoute } from "hono-openapi";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { jsonContent } from "stoker/openapi/helpers";

import env from "@/env";
import { API_TAGS } from "@/lib/constants";
import { createRouter } from "@/lib/helpers";
import { TelegramBotService } from "@/lib/telegram/bot-service";

const router = createRouter()
  .get(
    "/post-vacancies",
    describeRoute({
      title: "Trigger Vacancy Posts",
      tags: [API_TAGS.SYSTEM],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
            },
          },
          "Vacancy post trigger response",
        ),
      },
    }),
    async (c) => {
      if (!env.isDevelopment) {
        return c.json(
          { success: false, message: "Only available in development mode" },
          HttpStatusCodes.FORBIDDEN,
        );
      }
      const result = await TelegramBotService.triggerActiveVacanciesPost();
      return c.json(result, HttpStatusCodes.OK);
    },
  )
  .get(
    "/update-statuses",
    describeRoute({
      title: "Trigger Status Updates",
      tags: [API_TAGS.SYSTEM],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
            },
          },
          "Status update trigger response",
        ),
      },
    }),
    async (c) => {
      if (!env.isDevelopment) {
        return c.json(
          { success: false, message: "Only available in development mode" },
          HttpStatusCodes.FORBIDDEN,
        );
      }
      const result = await TelegramBotService.triggerStatusUpdates();
      return c.json(result, HttpStatusCodes.OK);
    },
  )
  .get(
    "/health-check",
    describeRoute({
      title: "Trigger Health Check",
      tags: [API_TAGS.SYSTEM],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
            },
          },
          "Health check trigger response",
        ),
      },
    }),
    async (c) => {
      if (!env.isDevelopment) {
        return c.json(
          { success: false, message: "Only available in development mode" },
          HttpStatusCodes.FORBIDDEN,
        );
      }
      const result = await TelegramBotService.triggerHealthCheck();
      return c.json(result, HttpStatusCodes.OK);
    },
  );

export default router;

import { pinoLogger as logger } from "hono-pino";
import createPino from "pino";
import pretty from "pino-pretty";

import env from "@/env";

const pino = createPino(
  {
    level: env.LOG_LEVEL || "info",
  },
  env.isProduction ? undefined : pretty(),
);

export function pinoLogger() {
  return logger({
    pino: createPino(
      {
        level: env.LOG_LEVEL || "info",
      },
      env.isProduction ? undefined : pretty(),
    ),
    http: {
      reqId: () => crypto.randomUUID(),
    },
  });
}

export { pino as logger };

import { pgTable, text, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

import { user } from "./user.entity";

export const authorDetails = pgTable("author_details", {
  userId: text("user_id")
    .primaryKey()
    .references(() => user.id, { onDelete: "cascade" }),
  bio: text("bio"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at")
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const insertAuthorDetailsSchema = createInsertSchema(authorDetails);
export const selectAuthorDetailsSchema = createSelectSchema(authorDetails);
export const patchAuthorDetailsSchema =
  createSelectSchema(authorDetails).partial();

export type AuthorDetails = typeof authorDetails.$inferSelect;
export type NewAuthorDetails = typeof authorDetails.$inferInsert;

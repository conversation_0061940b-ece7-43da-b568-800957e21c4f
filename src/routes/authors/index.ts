import db from "@/db";
import { insertUserSchema, selectUserSchema, user } from "@/db/schema";
import { auth } from "@/lib/auth";
import { API_TAGS } from "@/lib/constants";
import { createRouter, jsonContent, validator } from "@/lib/helpers";
import { openApiResponses, responses } from "@/lib/responses";
import { AuthorDTO, listAuthorsDTO } from "@/lib/schemas";
import { paginationParamsSchema } from "@/lib/schemas";
import { authGuard } from "@/middlewares/auth.middleware";
import { permissionGuard } from "@/middlewares/permission.middleware";
import { count, eq } from "drizzle-orm";
import { describeRoute } from "hono-openapi";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { z } from "zod";

const createUserSchema = insertUserSchema
  .omit({
    id: true,
    role: true,
    emailVerified: true,
    createdAt: true,
    updatedAt: true,
    archivedAt: true,
    banned: true,
    banReason: true,
    banExpires: true,
  })
  .extend({
    password: z.string().min(8),
  });

const router = createRouter()
  .use(authGuard)
  .get(
    "/",
    describeRoute({
      title: "List Authors",
      description: "Get a paginated list of all authors.",
      tags: [API_TAGS.AUTHORS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(listAuthorsDTO, "List of authors"),
      },
    }),
    permissionGuard({ author: ["read"] }),
    validator("query", paginationParamsSchema.optional()),
    async (c) => {
      const { page = 1, limit } = c.req.valid("query") || {};
      const offset = limit ? (page - 1) * limit : undefined;
      const [usersList, [{ total }]] = await Promise.all([
        db.query.user.findMany({
          where: { role: "author" },
          limit,
          offset,
          orderBy: { createdAt: "desc" },
          with: {
            authorDetails: true,
          },
          columns: {
            id: true,
            name: true,
            email: true,
            image: true,
            phoneNumber: true,
            dateOfBirth: true,
            gender: true,
            role: true,
            createdAt: true,
            updatedAt: true,
          },
        }),
        db.select({ total: count() }).from(user).where(eq(user.role, "author")),
      ]);
      const items = usersList.map(({ authorDetails, ...u }) => ({
        ...u,
        bio: authorDetails?.bio ?? null,
      }));
      return c.json(
        {
          items,
          total,
          page,
          limit,
          pages: limit ? Math.ceil(total / limit) : undefined,
        },
        HttpStatusCodes.OK,
      );
    },
  )
  .get(
    "/:id",
    describeRoute({
      title: "Get Author by ID",
      description: "Get a single author by user ID.",
      tags: [API_TAGS.AUTHORS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(AuthorDTO, "Author details"),
        ...openApiResponses.notFound,
      },
    }),
    permissionGuard({ author: ["read"] }),
    async (c) => {
      const { id } = c.req.param();
      const found = await db.query.user.findFirst({
        where: { id },
        with: { authorDetails: true },
        columns: {
          id: true,
          name: true,
          email: true,
          image: true,
          phoneNumber: true,
          dateOfBirth: true,
          gender: true,
          role: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      if (!found || found.role !== "author") {
        return responses.notFound(c, `Author with ID ${id}`);
      }
      const { authorDetails, ...u } = found;
      return c.json(
        {
          ...u,
          bio: authorDetails?.bio ?? null,
        },
        HttpStatusCodes.OK,
      );
    },
  )
  .post(
    "/",
    describeRoute({
      title: "Create Author",
      description:
        "Create a new author user. Requires author creation permission.",
      tags: [API_TAGS.AUTHORS],
      responses: {
        [HttpStatusCodes.CREATED]: jsonContent(
          selectUserSchema,
          "Created author user",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    permissionGuard({ author: ["create"] }),
    validator("json", createUserSchema),
    async (c) => {
      const data = c.req.valid("json");
      const result = await auth.api.createUser({
        body: {
          ...data,
          role: "author",
          emailVerified: true,
        },
        headers: c.req.raw.headers,
      });

      return c.json(result.user, HttpStatusCodes.CREATED);
    },
  );

export default router;

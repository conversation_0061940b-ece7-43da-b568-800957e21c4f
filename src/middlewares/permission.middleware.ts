import { auth } from "@/lib/auth";
import type { Permissions } from "@/lib/auth/roles";
import { responses } from "@/lib/responses";
import type { Context, Next } from "hono";

export function permissionGuard(permission: Permissions) {
  return async (c: Context, next: Next) => {
    const { success } = await auth.api.userHasPermission({
      body: {
        userId: c.var.checkedUser?.id,
        permission,
      },
    });

    if (!success) {
      return responses.forbidden(c);
    }

    return next();
  };
}

import { eq } from "drizzle-orm";

import db from "@/db";
import {
  type NewAdminDetails,
  type NewAuthorDetails,
  type NewEditorDetails,
  adminDetails,
  authorDetails,
  editorDetails,
} from "@/db/schema";

export class RoleDetailsOperations {
  static async createRoleDetails(userId: string, role: string) {
    await db.transaction(async (tx) => {
      let exists = false;
      switch (role) {
        case "admin": {
          exists = !!(await tx.query.adminDetails.findFirst({
            where: {
              userId,
            },
          }));
          if (!exists) {
            const data: NewAdminDetails = { userId };
            await tx.insert(adminDetails).values(data);
          }
          break;
        }
        case "author": {
          exists = !!(await tx.query.authorDetails.findFirst({
            where: {
              userId,
            },
          }));
          if (!exists) {
            const data: NewAuthorDetails = { userId };
            await tx.insert(authorDetails).values(data);
          }
          break;
        }
        case "editor": {
          exists = !!(await tx.query.editorDetails.findFirst({
            where: {
              userId,
            },
          }));
          if (!exists) {
            const data: NewEditorDetails = { userId };
            await tx.insert(editorDetails).values(data);
          }
          break;
        }
      }
    });
  }

  static async deleteRoleDetails(userId: string) {
    await db.transaction(async (tx) => {
      await tx.delete(adminDetails).where(eq(adminDetails.userId, userId));
      await tx.delete(authorDetails).where(eq(authorDetails.userId, userId));
      await tx.delete(editorDetails).where(eq(editorDetails.userId, userId));
    });
  }
}

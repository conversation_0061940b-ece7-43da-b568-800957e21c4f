import type { Context } from "hono";

import * as HttpStatusCodes from "stoker/http-status-codes";
import * as HttpStatusPhrases from "stoker/http-status-phrases";

import { jsonContent } from "./helpers";
import {
  forbiddenSchema,
  notFoundSchema,
  unauthorizedSchema,
  unprocessableEntitySchema,
} from "./schemas";

export const responses = {
  unauthorized(c: Context) {
    return c.json(
      {
        message: HttpStatusPhrases.UNAUTHORIZED,
        code: HttpStatusCodes.UNAUTHORIZED,
      },
      HttpStatusCodes.UNAUTHORIZED,
    );
  },

  forbidden(c: Context) {
    return c.json(
      {
        message: HttpStatusPhrases.FORBIDDEN,
        code: HttpStatusCodes.FORBIDDEN,
      },
      HttpStatusCodes.FORBIDDEN,
    );
  },

  notFound(c: Context, resource: string) {
    return c.json(
      {
        message: `${resource} not found`,
        code: HttpStatusCodes.NOT_FOUND,
      },
      HttpStatusCodes.NOT_FOUND,
    );
  },

  conflict(c: Context, message: string) {
    return c.json(
      {
        message,
        code: HttpStatusCodes.CONFLICT,
      },
      HttpStatusCodes.CONFLICT,
    );
  },

  noContent(c: Context) {
    return c.body(null, HttpStatusCodes.NO_CONTENT);
  },

  unprocessableEntity(c: Context, message: string) {
    return c.json(
      {
        message,
        code: HttpStatusCodes.UNPROCESSABLE_ENTITY,
      },
      HttpStatusCodes.UNPROCESSABLE_ENTITY,
    );
  },
};

export const openApiResponses = {
  unauthorized: {
    [HttpStatusCodes.UNAUTHORIZED]: jsonContent(
      unauthorizedSchema,
      "Unauthorized",
    ),
  },
  notFound: {
    [HttpStatusCodes.NOT_FOUND]: jsonContent(notFoundSchema, "Not Found"),
  },
  unprocessableEntity: {
    [HttpStatusCodes.UNPROCESSABLE_ENTITY]: jsonContent(
      unprocessableEntitySchema,
      "Unprocessable Entity",
    ),
  },
  forbidden: {
    [HttpStatusCodes.FORBIDDEN]: jsonContent(forbiddenSchema, "Forbidden"),
  },
  conflict: {
    [HttpStatusCodes.CONFLICT]: jsonContent(
      unprocessableEntitySchema,
      "Conflict",
    ),
  },
  tooManyRequests: {
    [HttpStatusCodes.TOO_MANY_REQUESTS]: jsonContent(
      unprocessableEntitySchema,
      "Too Many Requests",
    ),
  },
};

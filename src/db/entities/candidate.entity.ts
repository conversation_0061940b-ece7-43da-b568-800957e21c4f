import { integer, jsonb, pgTable, text, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";

import { generateId } from "@/lib/helpers";
import { user } from "./user.entity";

// Candidate statuses
export const CANDIDATE_STATUSES = [
  "new",
  "vacancy_selection",
  "filling_resume",
  "internal_interview",
  "waiting_on_site",
  "working",
  "visa_invitation",
  "transferred_to_eu",
  "transferred_to_employer",
  "not_suitable",
] as const;

// Candidate sources
export const CANDIDATE_SOURCES = [
  "facebook",
  "instagram",
  "google",
  "chat",
] as const;

export const candidate = pgTable("candidate", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => generateId()),

  // Basic info
  fullName: text("full_name").notNull(),
  phoneNumbers: jsonb("phone_numbers").$type<string[]>().notNull(),
  age: integer("age"),
  citizenship: text("citizenship"),

  // Professional info
  specialization: text("specialization"),
  specialtyExperience: text("specialty_experience"),
  foreignExperience: text("foreign_experience"),
  totalExperience: text("total_experience"),
  languageSkills: text("language_skills"),
  drivingLicense: text("driving_license"),
  travelDocument: text("travel_document"),

  // Current status
  currentCountry: text("current_country"),
  currentJob: text("current_job"),
  interestedCountry: text("interested_country"),
  availabilityDate: text("availability_date"),

  // Application management
  status: text("status", { enum: CANDIDATE_STATUSES }).notNull().default("new"),
  statusComment: text("status_comment"),
  responsibleManagerId: text("responsible_manager_id").references(
    () => user.id,
  ),
  interestedVacancies: jsonb("interested_vacancies")
    .$type<string[]>()
    .default([]),
  candidateComment: text("candidate_comment"),
  source: text("source", { enum: CANDIDATE_SOURCES }),
  referral: text("referral"),

  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at")
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const insertCandidateSchema = createInsertSchema(candidate, {
  phoneNumbers: z.array(z.string().min(1)),
  interestedVacancies: z.array(z.string()).optional(),
});

export const selectCandidateSchema = createSelectSchema(candidate)
  .omit({
    interestedVacancies: true,
  })
  .extend({
    interestedVacancies: z.array(z.string()).optional().nullable(),
  });

export const patchCandidateSchema = selectCandidateSchema.partial().omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export type Candidate = typeof candidate.$inferSelect;
export type NewCandidate = typeof candidate.$inferInsert;

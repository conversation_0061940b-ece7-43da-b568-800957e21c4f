import type { Context, Next } from "hono";

import { auth } from "@/lib/auth";
import { responses } from "@/lib/responses";

export async function authMiddleware(c: Context, next: Next) {
  const session = await auth.api.getSession({ headers: c.req.raw.headers });

  if (!session) {
    c.set("user", null);
    c.set("session", null);
    return next();
  }
  c.set("user", session.user);
  c.set("session", session.session);
  c.set("checkedUser", session.user);

  return next();
}

export async function authGuard(c: Context, next: Next) {
  if (!c.var.user) {
    return responses.unauthorized(c);
  }

  if (!c.var.user.emailVerified) {
    return responses.forbidden(c);
  }

  return next();
}

export async function roleGuard(c: Context, next: Next, role: string) {
  if (!c.var.user) {
    return responses.unauthorized(c);
  }

  if (c.var.user.role !== role) {
    return responses.forbidden(c);
  }

  return next();
}

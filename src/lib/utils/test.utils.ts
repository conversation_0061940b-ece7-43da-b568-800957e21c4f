import { execSync } from "node:child_process";
import fs from "node:fs";
import { auth } from "@/lib/auth";

export const TEST_DB_PATH = "test.db";

export async function setupTestUser(role = "author") {
  let response: Response | null = null;

  response = await auth.api
    .signInEmail({
      body: {
        email: `${role}@example.com`,
        password: "test-password-123",
      },
      asResponse: true,
    })
    .catch(() => null);

  if (!response || response.status !== 200) {
    response = await auth.api.signUpEmail({
      body: {
        name: `Test ${role}`,
        email: `${role}@example.com`,
        password: "test-password-123",
        firstName: "Test",
        lastName: role,
        role,
      },
      asResponse: true,
    });
  }

  const responseJson = await response.json();
  const cookieHeader = response.headers.get("set-cookie");
  const cookies = cookieHeader ? cookieHeader.split(",") : [];

  if (!responseJson.token || !cookies.length) {
    throw new Error("Failed to create test user");
  }

  const authToken = responseJson.token;
  const sessionCookie = cookies.find((cookie) =>
    cookie.trim().startsWith("better-auth.session_token="),
  );

  if (!sessionCookie) {
    throw new Error("No session cookie found in response");
  }

  return {
    authToken,
    sessionCookie,
    addAuthHeaders: () => ({
      Authorization: `Bearer ${authToken}`,
      Cookie: sessionCookie,
      Host: "localhost:9999",
      Origin: "http://localhost:9999",
    }),
  };
}

export function setupTestDb() {
  execSync("pnpm drizzle-kit push");
}

export function cleanupTestDb() {
  try {
    fs.rmSync(TEST_DB_PATH, { force: true });
  } catch (_error) {}
}

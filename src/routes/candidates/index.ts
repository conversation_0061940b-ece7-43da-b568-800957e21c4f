import db from "@/db";
import { API_TAGS } from "@/lib/constants";
import { createRouter, jsonContent, validator } from "@/lib/helpers";
import { openApiResponses, responses } from "@/lib/responses";
import { authGuard } from "@/middlewares/auth.middleware";
import { permissionGuard } from "@/middlewares/permission.middleware";
import { describeRoute } from "hono-openapi";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { z } from "zod";
import { jobApplicationWithCandidateDTO } from "../job-applications/dtos";
import {
  candidateWithApplicationsDTO,
  createCandidateDTO,
  listCandidatesDTO,
  listCandidatesQueryDTO,
  mergeCandidatesDTO,
  updateCandidateDTO,
} from "./dtos";
import { CandidateOperations } from "./operations";

const router = createRouter()
  .get(
    "/",
    authGuard,
    describeRoute({
      title: "List Candidates",
      description: "List all candidates with optional filtering",
      tags: [API_TAGS.CANDIDATES || "Candidates"],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          listCandidatesDTO,
          "List of candidates",
        ),
        ...openApiResponses.unauthorized,
      },
    }),
    permissionGuard({ candidate: ["read"] }),
    validator("query", listCandidatesQueryDTO.optional()),
    async (c) => {
      const query = c.req.valid("query");
      const candidates = await CandidateOperations.findMany(query);
      return c.json(candidates, HttpStatusCodes.OK);
    },
  )
  .get(
    "/:id",
    authGuard,
    describeRoute({
      title: "Get Candidate",
      description: "Get a candidate by ID with their applications",
      tags: [API_TAGS.CANDIDATES || "Candidates"],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          candidateWithApplicationsDTO,
          "Candidate with applications",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.notFound,
      },
    }),
    permissionGuard({ candidate: ["read"] }),
    async (c) => {
      const { id } = c.req.param();
      const candidate = await CandidateOperations.findByIdWithApplications(id);

      if (!candidate) {
        return responses.notFound(c, `Candidate with ID ${id}`);
      }

      return c.json(candidate, HttpStatusCodes.OK);
    },
  )
  .post(
    "/",
    authGuard,
    describeRoute({
      title: "Create Candidate",
      description: "Create a new candidate",
      tags: [API_TAGS.CANDIDATES || "Candidates"],
      responses: {
        [HttpStatusCodes.CREATED]: jsonContent(
          candidateWithApplicationsDTO,
          "Created candidate",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    permissionGuard({ candidate: ["create"] }),
    validator("json", createCandidateDTO),
    async (c) => {
      const data = c.req.valid("json");
      const candidate = await CandidateOperations.create(data);
      return c.json(candidate, HttpStatusCodes.CREATED);
    },
  )
  .patch(
    "/:id",
    authGuard,
    describeRoute({
      title: "Update Candidate",
      description: "Update a candidate by ID",
      tags: [API_TAGS.CANDIDATES || "Candidates"],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          candidateWithApplicationsDTO,
          "Updated candidate",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.notFound,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    permissionGuard({ candidate: ["update"] }),
    validator("json", updateCandidateDTO),
    async (c) => {
      const { id } = c.req.param();
      const updates = c.req.valid("json");

      const candidate = await CandidateOperations.findById(id);
      if (!candidate) {
        return responses.notFound(c, `Candidate with ID ${id}`);
      }

      const updated = await CandidateOperations.update(id, updates);
      return c.json(updated, HttpStatusCodes.OK);
    },
  )
  .post(
    "/:id/merge",
    authGuard,
    describeRoute({
      title: "Merge Candidates",
      description:
        "Merge another candidate into the specified candidate. All applications from the source candidate will be transferred to the target candidate, phone numbers will be merged, and the source candidate will be deleted.",
      tags: [API_TAGS.CANDIDATES || "Candidates"],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          candidateWithApplicationsDTO,
          "Merged candidate with all applications",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.notFound,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    permissionGuard({ candidate: ["merge"] }),
    validator("json", mergeCandidatesDTO),
    async (c) => {
      const { id: targetCandidateId } = c.req.param();
      const { sourceCandidateId } = c.req.valid("json");

      try {
        const mergedCandidate = await CandidateOperations.mergeCandidates(
          targetCandidateId,
          sourceCandidateId,
        );

        if (!mergedCandidate) {
          return responses.unprocessableEntity(c, "Failed to merge candidates");
        }

        return c.json(mergedCandidate, HttpStatusCodes.OK);
      } catch (error) {
        return responses.unprocessableEntity(c, (error as Error).message);
      }
    },
  )
  .get(
    "/:id/applications",
    authGuard,
    describeRoute({
      title: "Get Candidate Applications",
      description: "Get all applications for a specific candidate",
      tags: [API_TAGS.CANDIDATES || "Candidates"],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          z.array(jobApplicationWithCandidateDTO),
          "List of applications for the candidate",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
      },
    }),
    permissionGuard({ jobApplication: ["read"] }),
    async (c) => {
      const { id: candidateId } = c.req.param();
      const applications = await db.query.jobApplication.findMany({
        where: { candidateId },
        with: {
          candidate: true,
          jobVacancy: true,
        },
      });
      return c.json(applications, HttpStatusCodes.OK);
    },
  )
  .delete(
    "/:id",
    authGuard,
    describeRoute({
      title: "Delete Candidate",
      description: "Delete a candidate by ID",
      tags: [API_TAGS.CANDIDATES || "Candidates"],
      responses: {
        [HttpStatusCodes.NO_CONTENT]: {
          description: "Candidate deleted successfully",
        },
        ...openApiResponses.unauthorized,
        ...openApiResponses.notFound,
      },
    }),
    permissionGuard({ candidate: ["delete"] }),
    async (c) => {
      const { id } = c.req.param();

      const candidate = await CandidateOperations.findById(id);
      if (!candidate) {
        return responses.notFound(c, `Candidate with ID ${id}`);
      }

      await CandidateOperations.delete(id);
      return c.body(null, HttpStatusCodes.NO_CONTENT);
    },
  );

export default router;

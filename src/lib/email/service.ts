import { readFileSync } from "node:fs";
import path from "node:path";
import type { User } from "@/db/schema";
import Handlebars from "handlebars";
import { emailConfig, getResendClient } from "./config";

export class EmailService {
  private templates: Record<string, Handlebars.TemplateDelegate> = {};

  constructor() {
    this.templates.welcome = Handlebars.compile(
      readFileSync(
        path.join(process.cwd(), "src/lib/email/templates/welcome/html.hbs"),
        "utf-8",
      ),
    );
    this.templates.resetPassword = Handlebars.compile(
      readFileSync(
        path.join(
          process.cwd(),
          "src/lib/email/templates/reset-password/html.hbs",
        ),
        "utf-8",
      ),
    );
  }

  async sendWelcomeEmail(user: Partial<User>, verificationUrl: string) {
    const html = this.templates.welcome({
      appName: "Group Working CRM",
      firstName: user.name,
      verificationUrl,
    });

    const resend = getResendClient();
    return resend.emails.send({
      from: emailConfig.from,
      to: user.email!,
      subject: "Welcome to Group Working CRM",
      html,
    });
  }

  async sendPasswordResetEmail(user: Partial<User>, resetUrl: string) {
    const html = this.templates.resetPassword({
      firstName: user.name,
      resetUrl,
    });

    const resend = getResendClient();
    return resend.emails.send({
      from: emailConfig.from,
      to: user.email!,
      subject: "Reset Your Password",
      html,
    });
  }

  async sendVerificationEmail(user: Partial<User>, verificationUrl: string) {
    return this.sendWelcomeEmail(user, verificationUrl);
  }
}

export const emailService = new EmailService();

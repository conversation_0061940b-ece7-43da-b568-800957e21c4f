import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { admin, createAuthMiddleware, openAPI } from "better-auth/plugins";

import db from "@/db";
import * as schema from "@/db/schema";
import env from "@/env";
import { emailService } from "@/lib/email/service";
import { RoleDetailsOperations } from "@/routes/role-details/operations";
import type { AppOpenAPI } from "../types";

import { DEFAULT_ROLE, ac, roles } from "./roles";
import { userConfig } from "./user-config";

export const auth = betterAuth({
  basePath: "/auth",
  baseURL: env.BASE_URL,
  trustedOrigins: ["*"],
  user: {
    ...userConfig,
    changeEmail: {
      enabled: true,
    },
  },
  emailAndPassword: {
    enabled: true,
    sendResetPassword: async ({ user, url }) => {
      await emailService.sendPasswordResetEmail(user, url);
    },
    resetPasswordExpiresIn: 3600,
  },
  emailVerification: {
    sendVerificationEmail: async ({ user, url }) => {
      await emailService.sendVerificationEmail(user, url);
    },
    sendOnSignUp: true,
    autoSignInAfterVerification: true,
    expiresIn: 3600,
  },
  plugins: [
    openAPI({
      disableDefaultReference: true,
    }),
    admin({
      defaultRole: DEFAULT_ROLE,
      ac,
      roles,
    }),
  ],
  database: drizzleAdapter(db, {
    provider: "pg",
    schema,
  }),
  env: env.isDevelopment ? "DEV" : "PROD",
  secret: env.BETTER_AUTH_SECRET,
  advanced: {
    crossSubDomainCookies: {
      enabled: true,
      baseDomain: ".crm-group-working.com",
    },
    defaultCookieAttributes: {
      sameSite: env.isProduction ? "lax" : "none",
      secure: env.isProduction,
    },
  },
  hooks: {
    after: createAuthMiddleware(async (ctx) => {
      if (ctx.path.startsWith("/sign-up")) {
        const session = ctx.context.newSession;

        if (session) {
          await RoleDetailsOperations.createRoleDetails(
            session.user.id,
            session.user.role,
          );
        }
      } else if (
        ctx.path.startsWith("/admin/create-user") ||
        ctx.path.startsWith("/admin/set-role")
      ) {
        const returnedUser = (ctx.context.returned as { user: schema.User })
          .user;
        if (returnedUser) {
          await RoleDetailsOperations.createRoleDetails(
            returnedUser.id,
            returnedUser.role,
          );
        }
      }
    }),
  },
});

export function configureAuth(app: AppOpenAPI) {
  app.on(["POST", "GET"], "/auth/*", (c) => {
    return auth.handler(c.req.raw);
  });
}

export type Auth = typeof auth;

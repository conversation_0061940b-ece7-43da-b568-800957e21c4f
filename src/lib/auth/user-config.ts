import { DEFAULT_ROLE } from "./roles";

export const userConfig = {
  additionalFields: {
    role: {
      type: "string",
      required: true,
      defaultValue: DEFAULT_ROLE,
      input: false,
    },
    phoneNumber: {
      type: "string",
      required: false,
      input: true,
    },
    dateOfBirth: {
      type: "string",
      required: false,
      input: true,
    },
    gender: {
      type: "string",
      required: false,
      input: true,
      enum: ["male", "female"],
    },
    archivedAt: {
      type: "number",
      required: false,
      input: false,
    },
  },
  deleteUser: {
    enabled: false,
  },
} as const;

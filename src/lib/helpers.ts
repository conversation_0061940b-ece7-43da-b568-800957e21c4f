import type { ValidationTargets } from "hono";
import type { ZodSchema } from "zod";

import { init } from "@paralleldrive/cuid2";
import { Hono } from "hono";
import { validator as honoValidator, resolver } from "hono-openapi/zod";
import { UNPROCESSABLE_ENTITY } from "stoker/http-status-codes";

import type { AppBindings } from "./types";

export function jsonContent<T extends ZodSchema>(
  schema: T,
  description: string,
) {
  return {
    content: {
      "application/json": {
        schema: resolver(schema),
      },
    },
    description,
  };
}

export function validator<
  T extends keyof ValidationTargets,
  S extends ZodSchema,
>(target: T, schema: S) {
  return honoValidator(target, schema, (result, c) => {
    if (!result.success) {
      return c.json(
        {
          message: "Validation Error",
          errors: result.error.errors,
          code: UNPROCESSABLE_ENTITY,
        },
        UNPROCESSABLE_ENTITY,
      );
    }
  });
}

const prefixes = {
  key: "key",
  api: "api",
  request: "req",
  workspace: "ws",
} as const;

const createId = init({
  length: 36,
});

export function generateId(prefix?: keyof typeof prefixes): string {
  return prefix ? `${prefixes[prefix]}_${createId()}` : createId();
}

export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/^-+|-+$/g, "")
    .substring(0, 100);
}

export function createRouter() {
  return new Hono<AppBindings>({ strict: false });
}

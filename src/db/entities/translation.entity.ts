import { SUPPORTED_LANGUAGES } from "@/lib/constants";
import { generateId } from "@/lib/helpers";
import { pgTable, text, timestamp, unique } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

export const translationKey = pgTable("translation_key", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => generateId()),
  defaultText: text("default_text").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at")
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const translation = pgTable(
  "translation",
  {
    textId: text("text_id")
      .notNull()
      .references(() => translationKey.id),
    language: text("language", { enum: SUPPORTED_LANGUAGES }).notNull(),
    content: text("content").notNull(),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at")
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [unique().on(table.textId, table.language)],
);

export const insertTranslationKeySchema = createInsertSchema(translationKey);
export const selectTranslationKeySchema = createSelectSchema(translationKey);

export const insertTranslationSchema = createInsertSchema(translation);
export const selectTranslationSchema = createSelectSchema(translation);

export type TranslationKey = typeof translationKey.$inferSelect;
export type NewTranslationKey = typeof translationKey.$inferInsert;

export type Translation = typeof translation.$inferSelect;
export type NewTranslation = typeof translation.$inferInsert;

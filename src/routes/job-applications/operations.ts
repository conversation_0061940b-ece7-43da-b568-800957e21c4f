import type { NewJobApplication } from "@/db/schema";

import db from "@/db";
import {
  type APPLICATION_SOURCES,
  type APPLICATION_STATUSES,
  type APPLICATION_TYPES,
  jobApplication,
  jobVacancy,
} from "@/db/schema";
import { and, count, eq, ilike } from "drizzle-orm";
import { CandidateOperations } from "../candidates/operations";
import { JobVacancyOperations } from "../job-vacancies/operations";
import type { ListApplicationsDTO } from "./dtos";

export class JobApplicationOperations {
  static async findMany(
    userId: string,
    role: string,
    params?: {
      page?: number;
      limit?: number;
      sortBy?: "createdAt" | "updatedAt";
      sortDirection?: "asc" | "desc";
      status?: (typeof APPLICATION_STATUSES)[number];
      applicationType?: (typeof APPLICATION_TYPES)[number];
      source?: (typeof APPLICATION_SOURCES)[number];
      responsibleManagerId?: string;
      search?: string;
      dateFrom?: string;
      dateTo?: string;
    },
  ): Promise<ListApplicationsDTO> {
    const {
      page,
      limit,
      sortBy = "createdAt",
      sortDirection = "desc",
      status,
      applicationType,
      source,
      responsibleManagerId,
      search,
      dateFrom,
      dateTo,
    } = params ?? {};

    const [applications, [{ total }]] = await Promise.all([
      db.query.jobApplication.findMany({
        where: {
          ...(status && { status }),
          ...(applicationType && { applicationType }),
          ...(source && { source }),
          ...(responsibleManagerId &&
            role === "admin" && { responsibleManagerId }),
          ...(role === "author" && {
            jobVacancy: {
              authorId: userId,
            },
          }),
          ...(role === "editor" && { responsibleManagerId: userId }),
        },
        limit,
        offset: limit && page ? (page - 1) * limit : undefined,
        orderBy: {
          [sortBy]: sortDirection,
        },
        with: {
          candidate: true,
          responsibleManager: {
            columns: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
      }),
      db
        .select({
          total: count(),
        })
        .from(jobApplication)
        .leftJoin(jobVacancy, eq(jobApplication.jobVacancyId, jobVacancy.id))
        .where(
          and(
            search ? ilike(jobApplication.fullName, `%${search}%`) : undefined,
            status ? eq(jobApplication.status, status) : undefined,
            source ? eq(jobApplication.source, source) : undefined,
            role === "admin" && responsibleManagerId
              ? eq(jobApplication.responsibleManagerId, responsibleManagerId)
              : undefined,
            role === "author" ? eq(jobVacancy.authorId, userId) : undefined,
            role === "editor"
              ? eq(jobApplication.responsibleManagerId, userId)
              : undefined,
          ),
        )
        .limit(1),
    ]);

    let filteredApplications = applications;
    if (search) {
      const searchLower = search.toLowerCase();
      filteredApplications = applications.filter(
        (app) =>
          app.fullName.toLowerCase().includes(searchLower) ||
          app.phoneNumber.includes(search) ||
          app.citizenship?.toLowerCase().includes(searchLower) ||
          app.comment?.toLowerCase().includes(searchLower),
      );
    }

    if (dateFrom || dateTo) {
      filteredApplications = filteredApplications.filter((app) => {
        const appDate = new Date(app.createdAt);
        if (dateFrom && appDate < new Date(dateFrom)) return false;
        if (dateTo && appDate > new Date(dateTo)) return false;
        return true;
      });
    }

    return {
      total: search || dateFrom || dateTo ? filteredApplications.length : total,
      page,
      limit,
      pages: limit
        ? Math.ceil(
            (search || dateFrom || dateTo
              ? filteredApplications.length
              : total) / limit,
          )
        : undefined,
      items: filteredApplications,
    };
  }
  static async getStats(userId: string, role: string) {
    const applications = await db.query.jobApplication.findMany({
      where: {
        ...(role !== "admin" && { responsibleManagerId: userId }),
      },
    });

    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const stats = {
      totalApplications: applications.length,
      byStatus: {} as Record<string, number>,
      byType: {} as Record<string, number>,
      bySource: {} as Record<string, number>,
      recentApplications: 0,
    };

    for (const app of applications) {
      stats.byStatus[app.status] = (stats.byStatus[app.status] || 0) + 1;

      stats.byType[app.applicationType] =
        (stats.byType[app.applicationType] || 0) + 1;

      if (app.source) {
        stats.bySource[app.source] = (stats.bySource[app.source] || 0) + 1;
      }

      if (app.createdAt >= sevenDaysAgo) {
        stats.recentApplications++;
      }
    }

    return stats;
  }

  static async findById(id: string) {
    return db.query.jobApplication.findFirst({
      where: { id },
    });
  }

  static async findByIdWithVacancy(id: string) {
    const application = await db.query.jobApplication.findFirst({
      where: { id },
      with: {
        candidate: true,
        responsibleManager: {
          columns: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    if (!application) {
      return null;
    }
    const vacancy = application.jobVacancyId
      ? await JobVacancyOperations.findById(application.jobVacancyId)
      : null;

    return {
      ...application,
      jobVacancy: vacancy,
    };
  }

  static async findManyByVacancyId(
    vacancyId: string,
    params?: {
      page?: number;
      limit?: number;
      sortBy?: "createdAt" | "updatedAt";
      sortDirection?: "asc" | "desc";
    },
  ): Promise<ListApplicationsDTO> {
    const {
      page,
      limit,
      sortBy = "createdAt",
      sortDirection = "desc",
    } = params ?? {};

    const [applications, [{ total }]] = await Promise.all([
      db.query.jobApplication.findMany({
        where: { jobVacancyId: vacancyId },
        limit,
        offset: limit && page ? (page - 1) * limit : undefined,
        orderBy: {
          [sortBy]: sortDirection,
        },
        with: {
          candidate: true,
          responsibleManager: {
            columns: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
      }),
      db
        .select({
          total: count(),
        })
        .from(jobApplication)
        .where(eq(jobApplication.jobVacancyId, vacancyId))
        .limit(1),
    ]);

    return {
      total,
      page,
      limit,
      pages: limit ? Math.ceil(total / limit) : undefined,
      items: applications,
    };
  }

  /**
   * Create application with candidate data (smart candidate matching)
   * This is the main method for handling job application submissions
   */
  static async createWithCandidateData(applicationData: {
    jobVacancyId?: string;
    citizenship?: string;
    fullName: string;
    phoneNumber: string;
    status?: (typeof APPLICATION_STATUSES)[number];
    applicationType: (typeof APPLICATION_TYPES)[number];
    source?: (typeof APPLICATION_SOURCES)[number];
    vacancyLink?: string;
    referral?: string;
    comment?: string;
    responsibleManagerId?: string;
  }) {
    const candidate = await CandidateOperations.findOrCreate({
      fullName: applicationData.fullName,
      phoneNumber: applicationData.phoneNumber,
      citizenship: applicationData.citizenship,
    });

    const application = await JobApplicationOperations.create({
      candidateId: candidate.id,
      jobVacancyId: applicationData.jobVacancyId,
      citizenship: applicationData.citizenship,
      fullName: applicationData.fullName,
      phoneNumber: applicationData.phoneNumber,
      status: applicationData.status || "contact_needed",
      applicationType: applicationData.applicationType,
      source: applicationData.source,
      vacancyLink: applicationData.vacancyLink,
      referral: applicationData.referral,
      comment: applicationData.comment,
      responsibleManagerId: applicationData.responsibleManagerId,
    });

    return {
      ...application,
      candidate,
    };
  }

  static async create(data: NewJobApplication) {
    const [newJobApplication] = await db
      .insert(jobApplication)
      .values(data)
      .returning();
    return newJobApplication;
  }

  static async update(id: string, updates: Partial<NewJobApplication>) {
    const [updated] = await db
      .update(jobApplication)
      .set({
        ...updates,
        updatedAt: new Date(),
      })
      .where(eq(jobApplication.id, id))
      .returning();
    return updated;
  }

  static async delete(id: string) {
    const [deleted] = await db
      .delete(jobApplication)
      .where(eq(jobApplication.id, id))
      .returning();
    return !!deleted;
  }

  static async bulkUpdate(
    applicationIds: string[],
    updates: {
      status?: (typeof APPLICATION_STATUSES)[number];
      responsibleManagerId?: string;
      comment?: string;
    },
  ) {
    const results = [];

    for (const id of applicationIds) {
      const updated = await JobApplicationOperations.update(id, updates);
      if (updated) {
        results.push(updated);
      }
    }

    return results;
  }
}

import db from "@/db";
import {
  type JobVacancy,
  jobVacancy,
  jobVacancySpecialty,
  languageRequirement,
} from "@/db/schema";
import type { SupportedLanguage } from "@/lib/constants";
import { COUNTRY_CODES } from "@/lib/constants";
import { generateSlug } from "@/lib/helpers";
import { TranslationService } from "@/lib/translations/service";
import { count, eq, sql } from "drizzle-orm";
import type {
  CitiesByCountryDTO,
  CreateJobDTO,
  JobWithAuthorDTO,
  ListJobsDTO,
  ListJobsWithAuthorDTO,
  PaginatedData,
  PublicJobDTO,
  RawJobResponseDTO,
  UpdateJobDTO,
} from "./dtos";

export class JobVacancyOperations {
  static async findMany(
    params?: {
      page?: number;
      limit?: number;
      status?: ListJobsDTO["items"][number]["status"];
      sortBy?: "createdAt" | "updatedAt";
      sortDirection?: "asc" | "desc";
      authorId?: string;
      country?: string;
      city?: string;
      experience?: string;
      salaryFrom?: number;
      salaryTo?: number;
      jobType?: string;
      genders?: string[];
      housing?: string;
      title?: string;
      description?: string;
      specialties?: string[];
      requiresOtherLanguages?: boolean;
    },
    includePrivateInfo = false,
  ): Promise<PaginatedData<PublicJobDTO | JobWithAuthorDTO>> {
    const {
      page,
      limit,
      status,
      sortBy = "createdAt",
      sortDirection = "desc",
      authorId,
      country,
      city,
      experience,
      salaryFrom,
      salaryTo,
      jobType,
      genders,
      housing,
      title,
      description,
      specialties: specialtiesQuery,
      requiresOtherLanguages,
    } = params ?? {};

    const [vacancies, notPaginatedVacancies] = await Promise.all([
      db.query.jobVacancy.findMany({
        where: {
          ...(status ? { status } : { NOT: { status: "draft" } }),
          ...(authorId && { authorId }),
          ...(country && {
            country: {
              ilike: `%${country}%`,
            },
          }),
          ...(experience && {
            experience: experience as
              | "required"
              | "not_required"
              | "minimal"
              | "not_required_but_preferred",
          }),
          ...(jobType && { jobType }),
          ...(housing && {
            housing: housing as
              | "provided"
              | "not_provided"
              | "partially_compensated",
          }),
          ...(salaryFrom || salaryTo || city || (genders && genders.length > 0)
            ? {
                AND: [
                  ...(salaryFrom
                    ? [
                        {
                          RAW: (table: typeof jobVacancy) =>
                            sql`${table.salaryFrom} IS NOT NULL AND ${table.salaryFrom} >= ${salaryFrom}`,
                        },
                      ]
                    : []),
                  ...(salaryTo
                    ? [
                        {
                          RAW: (table: typeof jobVacancy) =>
                            sql`${table.salaryTo} IS NOT NULL AND ${table.salaryTo} <= ${salaryTo}`,
                        },
                      ]
                    : []),
                  ...(city
                    ? [
                        {
                          RAW: (table: typeof jobVacancy) =>
                            sql`EXISTS (SELECT 1 FROM json_each(${table.cities}) WHERE value LIKE '%' || ${city} || '%')`,
                        },
                      ]
                    : []),
                  ...(genders && genders.length > 0
                    ? [
                        {
                          RAW: (table: typeof jobVacancy) => {
                            return sql`EXISTS (SELECT 1 FROM json_each(${table.genders}) WHERE value IN (${genders.includes("pair") ? "pair" : ""},${genders.includes("brigade") ? "brigade" : ""},${genders.includes("any") ? "any" : ""},${genders.includes("male") ? "male" : ""},${genders.includes("female") ? "female" : ""}))`;
                          },
                        },
                      ]
                    : []),
                ],
              }
            : {}),
          ...(title
            ? {
                titleTranslations: {
                  content: {
                    ilike: `%${title}%`,
                  },
                },
              }
            : {}),
          ...(description
            ? {
                descriptionTranslations: {
                  content: {
                    ilike: `%${description}%`,
                  },
                },
              }
            : {}),
          ...(specialtiesQuery && specialtiesQuery.length > 0
            ? {
                specialties: {
                  OR: specialtiesQuery.map((s) => ({
                    code: {
                      ilike: `%${s}%`,
                    },
                  })),
                },
              }
            : {}),
          ...(requiresOtherLanguages !== undefined
            ? {
                languageRequirements: requiresOtherLanguages
                  ? {
                      AND: [
                        { NOT: { language: "ru" } },
                        { NOT: { level: "none" } },
                      ],
                    }
                  : {
                      OR: [{ language: "ru" }, { level: "none" }],
                    },
              }
            : {}),
        },
        limit,
        offset: limit && page ? (page - 1) * limit : undefined,
        orderBy: {
          status: "desc",
          [sortBy as string]: sortDirection,
        },
        with: {
          titleTranslations: true,
          workwearTranslations: true,
          responsibilitiesTranslations: true,
          descriptionTranslations: true,
          workScheduleTranslations: true,
          specialties: {
            columns: {
              code: true,
            },
          },
          languageRequirements: {
            columns: {
              jobVacancyId: false,
            },
          },
          ...(includePrivateInfo
            ? {
                author: {
                  columns: {
                    id: true,
                    name: true,
                    image: true,
                  },
                },
              }
            : {}),
        },
      }),
      db.query.jobVacancy.findMany({
        where: {
          ...(status ? { status } : { NOT: { status: "draft" } }),
          ...(authorId && { authorId }),
          ...(country && {
            country: {
              ilike: `%${country}%`,
            },
          }),
          ...(experience && {
            experience: experience as
              | "required"
              | "not_required"
              | "minimal"
              | "not_required_but_preferred",
          }),
          ...(jobType && { jobType }),
          ...(housing && {
            housing: housing as
              | "provided"
              | "not_provided"
              | "partially_compensated",
          }),
          ...(salaryFrom || salaryTo || city || (genders && genders.length > 0)
            ? {
                AND: [
                  ...(salaryFrom
                    ? [
                        {
                          RAW: (table: typeof jobVacancy) =>
                            sql`${table.salaryFrom} IS NOT NULL AND ${table.salaryFrom} >= ${salaryFrom}`,
                        },
                      ]
                    : []),
                  ...(salaryTo
                    ? [
                        {
                          RAW: (table: typeof jobVacancy) =>
                            sql`${table.salaryTo} IS NOT NULL AND ${table.salaryTo} <= ${salaryTo}`,
                        },
                      ]
                    : []),
                  ...(city
                    ? [
                        {
                          RAW: (table: typeof jobVacancy) =>
                            sql`EXISTS (SELECT 1 FROM json_each(${table.cities}) WHERE value LIKE '%' || ${city} || '%')`,
                        },
                      ]
                    : []),
                  ...(genders && genders.length > 0
                    ? [
                        {
                          RAW: (table: typeof jobVacancy) => {
                            return sql`EXISTS (SELECT 1 FROM json_each(${table.genders}) WHERE value IN (${genders.includes("pair") ? "pair" : ""},${genders.includes("brigade") ? "brigade" : ""},${genders.includes("any") ? "any" : ""},${genders.includes("male") ? "male" : ""},${genders.includes("female") ? "female" : ""}))`;
                          },
                        },
                      ]
                    : []),
                ],
              }
            : {}),
          ...(title
            ? {
                titleTranslations: {
                  content: {
                    ilike: `%${title}%`,
                  },
                },
              }
            : {}),
          ...(description
            ? {
                descriptionTranslations: {
                  content: {
                    ilike: `%${description}%`,
                  },
                },
              }
            : {}),
          ...(specialtiesQuery && specialtiesQuery.length > 0
            ? {
                specialties: {
                  OR: specialtiesQuery.map((s) => ({
                    code: {
                      ilike: `%${s}%`,
                    },
                  })),
                },
              }
            : {}),
          ...(requiresOtherLanguages !== undefined
            ? {
                languageRequirements: requiresOtherLanguages
                  ? {
                      AND: [
                        { NOT: { language: "ru" } },
                        { NOT: { level: "none" } },
                      ],
                    }
                  : {
                      OR: [{ language: "ru" }, { level: "none" }],
                    },
              }
            : {}),
        },
        columns: {
          id: true,
        },
      }),
    ]);

    const total = notPaginatedVacancies.length;

    return {
      total,
      page,
      limit,
      pages: limit ? Math.ceil(total / limit) : undefined,
      items: vacancies.map(
        ({
          specialties,
          titleTranslations,
          workwearTranslations,
          responsibilitiesTranslations,
          descriptionTranslations,
          workScheduleTranslations,
          author,
          ...rest
        }) => {
          const baseVacancy = {
            ...rest,
            specialties: specialties.map((s) => s.code),
            title: TranslationService.formatTranslations(titleTranslations),
            workwear:
              TranslationService.formatTranslations(workwearTranslations),
            responsibilities: TranslationService.formatTranslations(
              responsibilitiesTranslations,
            ),
            description: TranslationService.formatTranslations(
              descriptionTranslations,
            ),
            workSchedule: TranslationService.formatTranslations(
              workScheduleTranslations,
            ),
          };

          // For public view, omit sensitive fields
          if (!includePrivateInfo) {
            const { authorId, editorId, notes, statistics, ...publicData } =
              baseVacancy;
            return publicData;
          }

          // For admin/editor view, include author info
          return {
            ...baseVacancy,
            author: author
              ? {
                  id: author.id,
                  name: author.name,
                  image: author.image,
                }
              : undefined,
          };
        },
      ),
    };
  }

  static async findById(
    id: string,
    includePrivateInfo = false,
    userId?: string,
  ): Promise<PublicJobDTO | JobWithAuthorDTO | null> {
    const vacancy = await db.query.jobVacancy.findFirst({
      where: { id },
      with: {
        titleTranslations: true,
        workwearTranslations: true,
        responsibilitiesTranslations: true,
        descriptionTranslations: true,
        workScheduleTranslations: true,
        specialties: {
          columns: {
            code: true,
          },
        },
        languageRequirements: {
          columns: {
            jobVacancyId: false,
          },
        },
        ...(includePrivateInfo
          ? {
              author: {
                columns: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            }
          : {}),
      },
    });

    if (!vacancy) {
      return null;
    }

    if (
      vacancy.status === "draft" &&
      !includePrivateInfo &&
      vacancy.authorId !== userId
    ) {
      return null;
    }

    const {
      specialties,
      titleTranslations,
      workwearTranslations,
      responsibilitiesTranslations,
      descriptionTranslations,
      workScheduleTranslations,
      author,
      languageRequirements,
      ...rest
    } = vacancy;

    const baseVacancy = {
      ...rest,
      specialties: specialties.map((s: { code: string }) => s.code),
      title: TranslationService.formatTranslations(titleTranslations),
      workwear: TranslationService.formatTranslations(workwearTranslations),
      responsibilities: TranslationService.formatTranslations(
        responsibilitiesTranslations,
      ),
      description: TranslationService.formatTranslations(
        descriptionTranslations,
      ),
      workSchedule: TranslationService.formatTranslations(
        workScheduleTranslations,
      ),
      languageRequirements,
    };

    // For public view, omit sensitive fields
    if (!includePrivateInfo) {
      const { authorId, editorId, notes, statistics, ...publicData } =
        baseVacancy;
      return publicData as PublicJobDTO;
    }

    // For admin/editor view, include author info
    return {
      ...baseVacancy,
      author: author
        ? {
            id: author.id,
            name: author.name,
            image: author.image,
          }
        : undefined,
    } as JobWithAuthorDTO;
  }

  static async create(data: CreateJobDTO): Promise<RawJobResponseDTO> {
    const { specialties, languageRequirements, ...vacancyData } = data;

    return await db.transaction(async (tx) => {
      // Get the next sequence number
      const [{ max }] = await tx
        .select({ max: sql<number>`max(sequence)` })
        .from(jobVacancy);
      const nextSequence = (max ?? 0) + 1;

      const baseSlug =
        vacancyData.title.en && generateSlug(vacancyData.title.en);
      const slug = `vacancy-${baseSlug ? `${baseSlug}-` : ""}${nextSequence}`;

      const translatedTitle =
        await TranslationService.createTextWithTranslations(
          vacancyData.title,
          "",
          tx,
        );

      let translatedWorkwearId: string | undefined = undefined;
      if (vacancyData.workwear) {
        translatedWorkwearId = (
          await TranslationService.createTextWithTranslations(
            vacancyData.workwear,
            "",
            tx,
          )
        ).id;
      }
      const translatedResponsibilities =
        await TranslationService.createTextWithTranslations(
          vacancyData.responsibilities,
          "",
          tx,
        );
      const translatedDescription =
        await TranslationService.createTextWithTranslations(
          vacancyData.description,
          "",
          tx,
        );
      const translatedWorkSchedule =
        await TranslationService.createTextWithTranslations(
          vacancyData.workSchedule,
          "",
          tx,
        );

      const [newVacancy] = await tx
        .insert(jobVacancy)
        .values({
          ...vacancyData,
          sequence: nextSequence,
          slug,
          titleTextId: translatedTitle.id,
          workwearTextId: translatedWorkwearId,
          responsibilitiesTextId: translatedResponsibilities.id,
          descriptionTextId: translatedDescription.id,
          workScheduleTextId: translatedWorkSchedule.id,
        })
        .returning();

      await tx.insert(jobVacancySpecialty).values(
        specialties.map((code) => ({
          jobVacancyId: newVacancy.id,
          specialtyCode: code,
        })),
      );

      await tx.insert(languageRequirement).values(
        languageRequirements.map((req) => ({
          jobVacancyId: newVacancy.id,
          language: req.language,
          level: req.level,
        })),
      );

      return newVacancy;
    });
  }

  static async update(
    id: string,
    updates: UpdateJobDTO,
    userId?: string,
    isAdminOrEditor?: boolean,
  ): Promise<RawJobResponseDTO | null> {
    const { specialties, ...vacancyUpdates } = updates;

    return await db.transaction(async (tx) => {
      const [job] = await Promise.all([
        tx.query.jobVacancy.findFirst({ where: { id } }),
        specialties
          ? tx
              .delete(jobVacancySpecialty)
              .where(eq(jobVacancySpecialty.jobVacancyId, id))
          : Promise.resolve(null),
      ]);

      if (!job) return null;
      if (job.authorId !== userId && !isAdminOrEditor) {
        //        {
        //   jobAuthorId: '0hfNkyuMmcpkWRYgvEfIAwyZusXgboiX',
        //   userId: 'wWuP0fTyS2MFovXn2RnKfZjFNasg0cWd',
        //   isAdminOrEditor: false
        // }
        return null;
      }

      const operations: Promise<unknown>[] = [];

      if (specialties?.length) {
        operations.push(
          tx.insert(jobVacancySpecialty).values(
            specialties.map((code) => ({
              jobVacancyId: id,
              specialtyCode: code,
            })),
          ),
        );
      }

      const translationFields = {
        title: job.titleTextId,
        workwear: job.workwearTextId,
        responsibilities: job.responsibilitiesTextId,
        description: job.descriptionTextId,
        workSchedule: job.workScheduleTextId,
      };

      for (const [field, textId] of Object.entries(translationFields)) {
        const updates = vacancyUpdates[field as keyof typeof vacancyUpdates];
        if (updates && textId) {
          operations.push(
            ...Object.entries(updates).map(([language, content]) =>
              TranslationService.updateTranslation(
                textId,
                language as SupportedLanguage,
                content,
                tx,
              ),
            ),
          );
        }
      }

      let workwearTextId = null;
      if (vacancyUpdates.workwear && !job.workwearTextId) {
        const translatedWorkwear =
          await TranslationService.createTextWithTranslations(
            vacancyUpdates.workwear,
            "",
            tx,
          );
        workwearTextId = translatedWorkwear.id;
      }

      if (operations.length > 0) {
        await Promise.all(operations);
      }

      if (Object.keys(vacancyUpdates).length > 0) {
        const [updated] = await tx
          .update(jobVacancy)
          .set({
            ...vacancyUpdates,
            ...(workwearTextId ? { workwearTextId } : {}),
          })
          .where(eq(jobVacancy.id, id))
          .returning();
        return updated;
      }

      return job;
    });
  }

  static async delete(id: string) {
    const [deleted] = await db
      .delete(jobVacancy)
      .where(eq(jobVacancy.id, id))
      .returning();
    return !!deleted;
  }

  static async getCitiesByCountry(): Promise<CitiesByCountryDTO> {
    const vacancies = await db.query.jobVacancy.findMany({
      where: {
        status: "published",
      },
      columns: {
        country: true,
        cities: true,
      },
    });

    const citiesByCountry: Record<string, Set<string>> = {};

    for (const countryCode of COUNTRY_CODES) {
      citiesByCountry[countryCode] = new Set();
    }

    for (const vacancy of vacancies) {
      if (
        vacancy.country &&
        COUNTRY_CODES.includes(
          vacancy.country as (typeof COUNTRY_CODES)[number],
        ) &&
        vacancy.cities &&
        Array.isArray(vacancy.cities)
      ) {
        for (const city of vacancy.cities) {
          if (city && typeof city === "string" && city.trim()) {
            citiesByCountry[vacancy.country].add(city.trim());
          }
        }
      }
    }

    const result: CitiesByCountryDTO = {};
    for (const [country, citiesSet] of Object.entries(citiesByCountry)) {
      if (citiesSet.size > 0) {
        result[country as keyof CitiesByCountryDTO] =
          Array.from(citiesSet).sort();
      }
    }

    return result;
  }

  static async findByAuthor(
    authorId: string,
    params?: {
      page?: number;
      limit?: number;
      status?: ListJobsDTO["items"][number]["status"];
      sortBy?: "createdAt" | "updatedAt";
      sortDirection?: "asc" | "desc";
    },
  ): Promise<ListJobsWithAuthorDTO> {
    const {
      page,
      limit,
      status,
      sortBy = "createdAt",
      sortDirection = "desc",
    } = params ?? {};

    const [vacancies, [{ total }]] = await Promise.all([
      db.query.jobVacancy.findMany({
        where: {
          authorId,
          ...(status ? { status } : undefined),
        },
        limit,
        offset: limit && page ? (page - 1) * limit : undefined,
        orderBy: {
          [sortBy as string]: sortDirection,
        },
        with: {
          titleTranslations: true,
          workwearTranslations: true,
          responsibilitiesTranslations: true,
          descriptionTranslations: true,
          workScheduleTranslations: true,
          specialties: {
            columns: {
              code: true,
            },
          },
          languageRequirements: {
            columns: {
              jobVacancyId: false,
            },
          },
          author: {
            columns: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
      }),
      db
        .select({
          total: count(),
        })
        .from(jobVacancy)
        .where(eq(jobVacancy.authorId, authorId))
        .limit(1),
    ]);

    return {
      total,
      page,
      limit,
      pages: limit ? Math.ceil(total / limit) : undefined,
      items: vacancies.map(
        ({
          specialties,
          titleTranslations,
          workwearTranslations,
          responsibilitiesTranslations,
          descriptionTranslations,
          workScheduleTranslations,
          author,
          ...rest
        }) => ({
          ...rest,
          specialties: specialties.map((s) => s.code),
          title: TranslationService.formatTranslations(titleTranslations),
          workwear: TranslationService.formatTranslations(workwearTranslations),
          responsibilities: TranslationService.formatTranslations(
            responsibilitiesTranslations,
          ),
          description: TranslationService.formatTranslations(
            descriptionTranslations,
          ),
          workSchedule: TranslationService.formatTranslations(
            workScheduleTranslations,
          ),
          author: author
            ? {
                id: author.id,
                name: author.name,
                image: author.image ?? undefined,
              }
            : undefined,
        }),
      ),
    };
  }

  static async findBySlug(
    slug: string,
    includePrivateInfo = false,
    userId?: string,
  ): Promise<PublicJobDTO | JobWithAuthorDTO | null> {
    const vacancy = await db.query.jobVacancy.findFirst({
      where: {
        slug,
        ...(includePrivateInfo ? {} : { status: { NOT: "draft" } }),
      },
      with: {
        titleTranslations: true,
        workwearTranslations: true,
        responsibilitiesTranslations: true,
        descriptionTranslations: true,
        workScheduleTranslations: true,
        specialties: {
          columns: {
            code: true,
          },
        },
        languageRequirements: {
          columns: {
            jobVacancyId: false,
          },
        },
        ...(includePrivateInfo
          ? {
              author: {
                columns: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            }
          : {}),
      },
    });

    if (!vacancy) {
      return null;
    }

    if (
      vacancy.status === "draft" &&
      !includePrivateInfo &&
      vacancy.authorId !== userId
    ) {
      return null;
    }

    const {
      specialties,
      titleTranslations,
      workwearTranslations,
      responsibilitiesTranslations,
      descriptionTranslations,
      workScheduleTranslations,
      author,
      languageRequirements,
      ...rest
    } = vacancy;

    const baseVacancy = {
      ...rest,
      specialties: specialties.map((s: { code: string }) => s.code),
      title: TranslationService.formatTranslations(titleTranslations),
      workwear: TranslationService.formatTranslations(workwearTranslations),
      responsibilities: TranslationService.formatTranslations(
        responsibilitiesTranslations,
      ),
      description: TranslationService.formatTranslations(
        descriptionTranslations,
      ),
      workSchedule: TranslationService.formatTranslations(
        workScheduleTranslations,
      ),
      languageRequirements,
    };

    if (!includePrivateInfo) {
      const { authorId, editorId, notes, statistics, ...publicData } =
        baseVacancy;
      return publicData as PublicJobDTO;
    }

    return {
      ...baseVacancy,
      author: author
        ? {
            id: author.id,
            name: author.name,
            image: author.image,
          }
        : undefined,
    } as JobWithAuthorDTO;
  }

  static async findRawById(id: string): Promise<JobVacancy | null> {
    return (
      (await db.query.jobVacancy.findFirst({
        where: { id },
      })) ?? null
    );
  }
}

import { defineRelations } from "drizzle-orm";
import * as schema from "./schema";

export const relations = defineRelations(schema, (r) => ({
  candidate: {
    jobApplications: r.many.jobApplication({
      from: r.candidate.id,
      to: r.jobApplication.candidateId,
    }),
    responsibleManager: r.one.user({
      from: r.candidate.responsibleManagerId,
      to: r.user.id,
    }),
  },
  jobApplication: {
    candidate: r.one.candidate({
      from: r.jobApplication.candidateId,
      to: r.candidate.id,
    }),
    jobVacancy: r.one.jobVacancy({
      from: r.jobApplication.jobVacancyId,
      to: r.jobVacancy.id,
    }),
    responsibleManager: r.one.user({
      from: r.jobApplication.responsibleManagerId,
      to: r.user.id,
    }),
  },

  jobVacancy: {
    jobApplications: r.many.jobApplication({
      from: r.jobVacancy.id,
      to: r.jobApplication.jobVacancyId,
    }),
    specialties: r.many.specialty({
      from: r.jobVacancy.id.through(r.jobVacancySpecialty.jobVacancyId),
      to: r.specialty.code.through(r.jobVacancySpecialty.specialtyCode),
    }),
    titleTranslations: r.many.translation({
      from: r.jobVacancy.titleTextId,
      to: r.translation.textId,
      alias: "title",
    }),
    workwearTranslations: r.many.translation({
      from: r.jobVacancy.workwearTextId,
      to: r.translation.textId,
      alias: "workwear",
    }),
    responsibilitiesTranslations: r.many.translation({
      from: r.jobVacancy.responsibilitiesTextId,
      to: r.translation.textId,
      alias: "responsibilities",
    }),
    descriptionTranslations: r.many.translation({
      from: r.jobVacancy.descriptionTextId,
      to: r.translation.textId,
      alias: "description",
    }),
    workScheduleTranslations: r.many.translation({
      from: r.jobVacancy.workScheduleTextId,
      to: r.translation.textId,
      alias: "workSchedule",
    }),
    languageRequirements: r.many.languageRequirement({
      from: r.jobVacancy.id,
      to: r.languageRequirement.jobVacancyId,
    }),
    author: r.one.user({
      from: r.jobVacancy.authorId,
      to: r.user.id,
      alias: "author",
    }),
    editor: r.one.user({
      from: r.jobVacancy.editorId,
      to: r.user.id,
      alias: "editor",
    }),
  },
  languageRequirement: {
    jobVacancy: r.one.jobVacancy({
      from: r.languageRequirement.jobVacancyId,
      to: r.jobVacancy.id,
    }),
  },
  user: {
    authorDetails: r.one.authorDetails({
      from: r.user.id,
      to: r.authorDetails.userId,
    }),
    managedCandidates: r.many.candidate({
      from: r.user.id,
      to: r.candidate.responsibleManagerId,
    }),
    managedApplications: r.many.jobApplication({
      from: r.user.id,
      to: r.jobApplication.responsibleManagerId,
    }),
  },
}));

import { pgTable, text, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

import { user } from "./user.entity";

export const adminDetails = pgTable("admin_details", {
  userId: text("user_id")
    .primaryKey()
    .references(() => user.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at")
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const insertAdminDetailsSchema = createInsertSchema(adminDetails);
export const selectAdminDetailsSchema = createSelectSchema(adminDetails);
export const patchAdminDetailsSchema =
  createSelectSchema(adminDetails).partial();

export type AdminDetails = typeof adminDetails.$inferSelect;
export type NewAdminDetails = typeof adminDetails.$inferInsert;

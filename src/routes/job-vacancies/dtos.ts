import { insertJobVacancySchema, selectJobVacancySchema } from "@/db/schema";
import {
  COUNTRY_CODES,
  GENDERS,
  REQUIREMENTS_LANGUAGES,
  SPECIALTY_CODES,
  WORKPLACE_CODES,
} from "@/lib/constants";
import {
  createPaginatedSchema,
  paginationParamsSchema,
  translatedFieldSchema,
} from "@/lib/schemas";
import { z } from "zod";

export const JobDTO = selectJobVacancySchema
  .omit({
    titleTextId: true,
    workwearTextId: true,
    responsibilitiesTextId: true,
    descriptionTextId: true,
    workScheduleTextId: true,
  })
  .extend({
    specialties: z.array(z.string()),
    title: translatedFieldSchema,
    workwear: translatedFieldSchema.optional().nullable(),
    responsibilities: translatedFieldSchema,
    description: translatedFieldSchema,
    workSchedule: translatedFieldSchema,
    languageRequirements: z.array(
      z.object({
        language: z.enum(REQUIREMENTS_LANGUAGES),
        level: z.enum(["none", "low", "mid", "high"]),
      }),
    ),
    cities: z.array(z.string()),
    genders: z.array(z.enum(GENDERS)).min(1),
    housing: z
      .enum(["provided", "not_provided", "partially_compensated"])
      .optional()
      .nullable(),
    workplace: z.enum(WORKPLACE_CODES).nullable().optional(),
  });
export const listJobsDTO = createPaginatedSchema(JobDTO);
export type ListJobsDTO = z.infer<typeof listJobsDTO>;
export type TJobDTO = z.infer<typeof JobDTO>;

export const listJobsQueryDTO = paginationParamsSchema
  .extend({
    status: z.enum(["published", "archived", "draft"]).optional(),
    authorId: z.string().optional(),
    country: z.enum(COUNTRY_CODES).optional(),
    city: z.string().optional(),
    experience: z
      .enum([
        "not_required",
        "minimal",
        "required",
        "not_required_but_preferred",
      ])
      .optional(),
    salaryFrom: z.coerce.number().optional(),
    salaryTo: z.coerce.number().optional(),
    jobType: z.string().optional(),
    genders: z
      .union([z.array(z.enum(GENDERS)), z.enum(GENDERS)])
      .transform((value) => (Array.isArray(value) ? value : [value]))
      .optional(),
    housing: z
      .enum(["provided", "not_provided", "partially_compensated"])
      .optional(),
    title: z.string().optional(),
    description: z.string().optional(),
    specialties: z
      .union([z.array(z.string()), z.string()])
      .transform((value) => (Array.isArray(value) ? value : [value]))
      .optional(),
    workplace: z.enum(WORKPLACE_CODES).optional(),
    requiresOtherLanguages: z.coerce.boolean().optional(),
  })
  .partial();

export const rawJobResponseDTO = selectJobVacancySchema;
export type RawJobResponseDTO = z.infer<typeof rawJobResponseDTO>;

export const citiesByCountryDTO = z.record(
  z.enum(COUNTRY_CODES),
  z.array(z.string()),
);
export type CitiesByCountryDTO = z.infer<typeof citiesByCountryDTO>;

export const createJobDTO = insertJobVacancySchema
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
    archivedAt: true,
    titleTextId: true,
    workwearTextId: true,
    responsibilitiesTextId: true,
    descriptionTextId: true,
    workScheduleTextId: true,
    sequence: true,
    slug: true,
  })
  .extend({
    specialties: z.array(z.enum(SPECIALTY_CODES)),
    title: translatedFieldSchema,
    workwear: translatedFieldSchema.optional().nullable(),
    responsibilities: translatedFieldSchema,
    description: translatedFieldSchema,
    workSchedule: translatedFieldSchema,
    languageRequirements: z.array(
      z.object({
        language: z.enum(REQUIREMENTS_LANGUAGES),
        level: z.enum(["none", "low", "mid", "high"]),
      }),
    ),
    cities: z.array(z.string().min(1)),
    genders: z.array(z.enum(GENDERS)).min(1),
    numberOfPositions: z.number().int().positive().default(1),
    housing: z
      .enum(["provided", "not_provided", "partially_compensated"])
      .optional(),
    workplace: z.enum(WORKPLACE_CODES).nullable().optional(),
  });
export type CreateJobDTO = z.infer<typeof createJobDTO>;

export const updateJobDTO = createJobDTO.partial();
export type UpdateJobDTO = z.infer<typeof updateJobDTO>;

export const PublicJobDTO = JobDTO.omit({
  authorId: true,
  editorId: true,
  notes: true,
  statistics: true,
});
export type PublicJobDTO = z.infer<typeof PublicJobDTO>;

export const listPublicJobsDTO = createPaginatedSchema(PublicJobDTO);
export type ListPublicJobsDTO = z.infer<typeof listPublicJobsDTO>;

export const AuthorInfoDTO = z.object({
  id: z.string(),
  name: z.string(),
  image: z.string().optional(),
});
export type AuthorInfoDTO = z.infer<typeof AuthorInfoDTO>;

export const JobWithAuthorDTO = JobDTO.extend({
  author: AuthorInfoDTO.optional(),
});
export type JobWithAuthorDTO = z.infer<typeof JobWithAuthorDTO>;

export const listJobsWithAuthorDTO = createPaginatedSchema(JobWithAuthorDTO);
export type ListJobsWithAuthorDTO = z.infer<typeof listJobsWithAuthorDTO>;

export type PaginatedData<T> = {
  total: number;
  page?: number;
  limit?: number;
  pages?: number;
  items: T[];
};

export type JobResponseItem = TJobDTO | PublicJobDTO | JobWithAuthorDTO;

export type ListJobsResponse = PaginatedData<JobResponseItem>;

import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "hono";

import type { APIError } from "@/lib/errors";
import type { AppBindings } from "@/lib/types";
import { HTTPException } from "hono/http-exception";
import { ZodError } from "zod";

export const errorHandler: <PERSON>rrorHandler<AppBindings> = (err, c) => {
  c.var.logger.error(err);

  if (err.name === "APIError") {
    const apiError = err as APIError;
    return c.json(
      {
        message: apiError.message || apiError.status,
        code: apiError.statusCode,
      },
      apiError.statusCode,
    );
  }

  if (err instanceof SyntaxError && err.message.includes("JSON")) {
    return c.json(
      {
        message: "Invalid JSON payload",
        code: 400,
      },
      400,
    );
  }

  if (err instanceof HTTPException) {
    // Preserve headers from the original HTTPException (important for WWW-Authenticate)
    const response = c.json(
      {
        message: err.message,
        code: err.status,
      },
      err.status,
    );

    // Copy headers from the original exception
    if (err.res?.headers) {
      for (const [key, value] of err.res.headers.entries()) {
        response.headers.set(key, value);
      }
    }

    return response;
  }

  if (err instanceof ZodError) {
    return c.json(
      {
        message: "Validation Error",
        errors: err.errors,
        code: 422,
      },
      422,
    );
  }

  return c.json(
    {
      message: "Internal Server Error",
      code: 500,
    },
    500,
  );
};

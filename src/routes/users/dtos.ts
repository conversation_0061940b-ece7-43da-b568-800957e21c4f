import { insertUserSchema } from "@/db/schema";
import { z } from "zod";

export const createUserSchema = insertUserSchema
  .omit({
    id: true,
    role: true,
    emailVerified: true,
    createdAt: true,
    updatedAt: true,
    archivedAt: true,
    banned: true,
    banReason: true,
    banExpires: true,
  })
  .extend({
    password: z.string().min(8),
  });

export const changeRoleSchema = z.object({
  role: z.enum(["admin", "author", "editor"]),
});

export const deleteAccountSchema = z.object({
  password: z.string().min(1, "Password is required"),
  deletionReason: z.string().min(1, "Deletion reason is required"),
});

export const vacancyAuthorSchema = z.object({
  id: z.string(),
  name: z.string(),
  image: z.string().nullable(),
});

export const vacancyAuthorsResponseSchema = z.array(vacancyAuthorSchema);

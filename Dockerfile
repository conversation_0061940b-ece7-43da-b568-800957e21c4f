# Build stage
FROM node:20-alpine as builder
WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile
COPY . .
RUN pnpm build

# Production stage
FROM node:20-alpine
WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile --prod
COPY --from=builder /app/dist ./dist
# Copy email templates
COPY --from=builder /app/src/lib/email/templates ./src/lib/email/templates

ENV NODE_ENV=production

CMD ["pnpm", "start"]

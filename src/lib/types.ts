import type { <PERSON><PERSON> } from "hono";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "hono-pino";

import type { Auth } from "./auth";

export interface AppBindings {
  Variables: {
    logger: PinoLogger;
    user: Auth["$Infer"]["Session"]["user"] | null;
    checkedUser: Auth["$Infer"]["Session"]["user"];
    session: Auth["$Infer"]["Session"]["session"] | null;
  };
}

export type AppOpenAPI = Hono<AppBindings>;

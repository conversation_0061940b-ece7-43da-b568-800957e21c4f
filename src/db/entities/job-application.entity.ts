import { pgTable, text, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

import { generateId } from "@/lib/helpers";
import { candidate } from "./candidate.entity";
import { jobVacancy } from "./job-vacancy.entity";
import { user } from "./user.entity";

// Application statuses
export const APPLICATION_STATUSES = [
  "not_suitable",
  "no_contact",
  "non_target",
  "duplicate",
  "contact_needed",
  "in_progress",
] as const;

// Application types
export const APPLICATION_TYPES = [
  "application_form",
  "vacancy_response",
  "karta_pobytu",
  "contact_request",
  "question",
  "chat",
  "callback_request",
  "partnership",
] as const;

// Application sources
export const APPLICATION_SOURCES = ["facebook", "instagram"] as const;

export const jobApplication = pgTable("job_application", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => generateId()),
  candidateId: text("candidate_id")
    .notNull()
    .references(() => candidate.id, { onDelete: "cascade" }),
  jobVacancyId: text("job_vacancy_id").references(() => jobVacancy.id, {
    onDelete: "cascade",
  }),

  // New fields for job applications
  citizenship: text("citizenship"),
  fullName: text("full_name").notNull(),
  phoneNumber: text("phone_number").notNull(),
  status: text("status", { enum: APPLICATION_STATUSES })
    .notNull()
    .default("contact_needed"),
  applicationType: text("application_type", {
    enum: APPLICATION_TYPES,
  }).notNull(),
  source: text("source", { enum: APPLICATION_SOURCES }),
  vacancyLink: text("vacancy_link"),
  referral: text("referral"),
  comment: text("comment"),
  responsibleManagerId: text("responsible_manager_id").references(
    () => user.id,
  ),

  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at")
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const insertJobApplicationSchema = createInsertSchema(jobApplication);
export const selectJobApplicationSchema = createSelectSchema(jobApplication);
export const patchJobApplicationSchema = selectJobApplicationSchema
  .partial()
  .omit({
    id: true,
    candidateId: true,
    createdAt: true,
    updatedAt: true,
  });

export type JobApplication = typeof jobApplication.$inferSelect;
export type NewJobApplication = typeof jobApplication.$inferInsert;

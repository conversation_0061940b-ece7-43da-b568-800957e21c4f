# Group Working CRM API

A modern, type-safe JSON API built with Hono and OpenAPI for managing CRM operations.

## Features

- 🔐 **Authentication & Authorization**
  - Role-based access control
  - Secure session management
  - Cross-domain cookie support
- 📚 **API Documentation**
  - OpenAPI/Swagger specification
  - Interactive API reference with Scalar
  - Type-safe routes with Zod validation
- 🛠️ **Developer Experience**
  - TypeScript support
  - Hot reload during development
  - Comprehensive testing setup
  - Docker support for development and production
- 🏗️ **Architecture**
  - Clean code structure
  - Modular design
  - Database migrations
  - Structured logging

## Quick Start

### Prerequisites

- Node.js 20+
- pnpm
- Docker (optional)

### Local Development

1. Clone the repository:
```bash
git clone https://github.com/your-username/gw-crm-api.git
cd gw-crm-api
```

2. Install dependencies:
```bash
pnpm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

4. Configure your `.env` file with required values:
```env
NODE_ENV=development
PORT=9999
LOG_LEVEL=debug
DATABASE_URL=your-database-url
BETTER_AUTH_SECRET=your-secret
BASE_URL=http://localhost:9999
```

5. Start the development server:
```bash
pnpm dev
```

### Docker Deployment

#### Development
```bash
# Build and start containers
pnpm docker:dev

# View logs
pnpm docker:dev:logs

# Stop containers
pnpm docker:dev:down
```

#### Production
```bash
# Build and start containers
pnpm docker:prod

# View logs
pnpm docker:prod:logs

# Stop containers
pnpm docker:prod:down
```

## API Documentation

- OpenAPI Specification: `/doc`
- Interactive API Reference: `/reference`
- Health Check: `/`

### Available Endpoints

| Method | Path                    | Description              | Authentication | Required Role |
|--------|------------------------|--------------------------|----------------|---------------|
| GET    | /                      | Health check             | No            | -             |
| GET    | /doc                   | OpenAPI specification    | No            | -             |
| GET    | /reference             | API documentation        | No            | -             |
| POST   | /auth/login            | User login               | No            | -             |
| POST   | /auth/register         | User registration        | No            | -             |
| GET    | /tasks                 | List tasks               | Yes           | Any           |
| POST   | /tasks                 | Create task              | Yes           | Any           |
| GET    | /job-vacancies         | List job vacancies       | No            | -             |
| POST   | /job-vacancies         | Create job vacancy       | Yes           | admin, author, editor |
| PATCH  | /job-vacancies/{id}    | Update job vacancy       | Yes           | admin, author*, editor |
| DELETE | /job-vacancies/{id}    | Delete job vacancy       | Yes           | admin         |
| GET    | /job-applications      | List applications        | Yes           | admin, author  |
| POST   | /job-applications      | Create application       | No            | -             |
| PATCH  | /job-applications/{id} | Update application status| Yes           | admin, author* |
| DELETE | /job-applications/{id} | Delete application       | Yes           | admin         |

\* Authors can only modify their own content

### Role-Based Access Control

The API implements the following roles with specific permissions:

#### Admin
- Full access to all features
- Can manage users and roles
- Can delete any content

#### Author
- Create and manage own job vacancies
- Review applications for own job vacancies
- View other job vacancies
- Cannot delete content

#### Editor
- Create and publish job vacancies
- Review and manage job applications
- Cannot delete content
- Cannot change user roles

## Development

### Available Scripts

```bash
# Development
pnpm dev         # Start development server
pnpm test        # Run tests
pnpm lint        # Lint code
pnpm format      # Format code

# Database
pnpm db:push     # Push schema changes
pnpm db:studio   # Open database UI

# Build
pnpm build       # Build for production
pnpm start       # Start production server
```

### Project Structure

```
src/
├── db/             # Database configuration and schemas
├── lib/            # Shared utilities and helpers
├── middlewares/    # HTTP middlewares
├── routes/         # API routes and handlers
└── app.ts          # Main application setup
```

## Testing

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Generate coverage report
pnpm test:coverage
```

## Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| NODE_ENV | Environment | No | development |
| PORT | Server port | No | 9999 |
| LOG_LEVEL | Logging level | Yes | info |
| DATABASE_URL | Database URL | Yes | - |
| DATABASE_AUTH_TOKEN | Database auth token | Production only | - |
| BETTER_AUTH_SECRET | Auth secret key | Yes | - |
| BASE_URL | API base URL | No | http://localhost:9999 |

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

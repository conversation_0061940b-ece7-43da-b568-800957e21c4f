import { pgTable, text, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

import { user } from "./user.entity";

export const editorDetails = pgTable("editor_details", {
  userId: text("user_id")
    .primaryKey()
    .references(() => user.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at")
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const insertEditorDetailsSchema = createInsertSchema(editorDetails);
export const selectEditorDetailsSchema = createSelectSchema(editorDetails);
export const patchEditorDetailsSchema =
  createSelectSchema(editorDetails).partial();

export type EditorDetails = typeof editorDetails.$inferSelect;
export type NewEditorDetails = typeof editorDetails.$inferInsert;

import { SPECIALTY_CODES } from "@/lib/constants";
import { pgTable, primaryKey, text } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { jobVacancy } from "./job-vacancy.entity";

export const specialty = pgTable("specialty", {
  code: text("code", {
    enum: SPECIALTY_CODES,
  }).primaryKey(),
});

export const jobVacancySpecialty = pgTable(
  "job_vacancy_specialty",
  {
    jobVacancyId: text("job_vacancy_id")
      .notNull()
      .references(() => jobVacancy.id, { onDelete: "cascade" }),
    specialtyCode: text("specialty_code", {
      enum: SPECIALTY_CODES,
    })
      .notNull()
      .references(() => specialty.code, { onDelete: "cascade" }),
  },
  (table) => [
    primaryKey({
      columns: [table.jobVacancyId, table.specialtyCode],
    }),
  ],
);

export const insertSpecialtySchema = createInsertSchema(specialty);
export const selectSpecialtySchema = createSelectSchema(specialty);

export type Specialty = typeof specialty.$inferSelect;
export type NewSpecialty = typeof specialty.$inferInsert;

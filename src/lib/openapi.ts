import type { Swagger } from "atlassian-openapi";

import { apiReference } from "@scalar/hono-api-reference";
import type { Context, Next } from "hono";
import { openAPISpecs } from "hono-openapi";
import { basicAuth } from "hono/basic-auth";
import { isErrorResult, merge } from "openapi-merge";

import env from "@/env";

import type { AppOpenAPI } from "./types";

import { auth } from "./auth";
import { API_TAGS, API_TAGS_ORDER } from "./constants";

function updateSwaggerTags(
  schema: Swagger.SwaggerV3,
  from: string,
  to: string,
): Swagger.SwaggerV3 {
  if (!schema.paths) return schema;

  for (const path in schema.paths) {
    const methods = schema.paths[path];
    for (const method in methods) {
      if (
        methods[method as Swagger.Method]?.tags?.includes(from) &&
        methods[method as Swagger.Method]
      ) {
        const methodObj = methods[method as Swagger.Method];
        if (methodObj) {
          methodObj.tags = methodObj.tags?.map((tag) =>
            tag === from ? to : tag,
          );
        }
      }
    }
  }

  return schema;
}

export function configureOpenAPI(app: AppOpenAPI) {
  const docsBasicAuth = () => {
    if (!env.DOCS_BASIC_AUTH_USERNAME || !env.DOCS_BASIC_AUTH_PASSWORD) {
      return async (_c: Context, next: Next) => next();
    }

    return basicAuth({
      username: env.DOCS_BASIC_AUTH_USERNAME,
      password: env.DOCS_BASIC_AUTH_PASSWORD,
      realm: "API Documentation",
    });
  };

  app.get(
    "/openapi",
    docsBasicAuth(),
    openAPISpecs(app, {
      documentation: {
        info: {
          title: "Group Working CRM API",
          version: "1.0.0",
          description: "Group Working CRM API",
        },
        servers: [
          {
            url: env.BASE_URL,
            description: env.isProduction
              ? "Production server"
              : "Development server",
          },
        ],
        tags: API_TAGS_ORDER,
      },
    }),
  );

  app.get("/doc", docsBasicAuth(), async (c, next) => {
    try {
      const nonAuthResponse = await openAPISpecs(app, {
        documentation: {
          info: {
            title: "Group Working CRM API",
            version: "1.0.0",
            description: "Group Working CRM API",
          },
          servers: [
            {
              url: env.BASE_URL,
              description: env.isProduction
                ? "Production server"
                : "Development server",
            },
          ],
          tags: API_TAGS_ORDER,
        },
      })(c, next);

      if (!nonAuthResponse) throw new Error("Failed to fetch API doc");

      const result = await nonAuthResponse.text();
      const authRef =
        (await auth.api.generateOpenAPISchema()) as Swagger.SwaggerV3;

      if (authRef.tags) {
        authRef.tags[0].name = API_TAGS.AUTH;
        authRef.tags[0].description = "Authentication handled by better-auth";
        updateSwaggerTags(authRef, "Default", API_TAGS.AUTH);
      }
      const mergeResult = merge([
        {
          oas: JSON.parse(result),
        },
        {
          oas: authRef,
          pathModification: {
            prepend: "/auth",
          },
        },
      ]);

      if (isErrorResult(mergeResult))
        return c.json({ error: "Failed to merge OpenAPI schemas" }, 500);

      return c.json(mergeResult.output);
    } catch (error) {
      return c.json(
        {
          error:
            error instanceof Error ? error.message : "Internal server error",
        },
        500,
      );
    }
  });
  app.get(
    "/reference",
    docsBasicAuth(),
    apiReference({
      theme: "alternate",
      layout: "modern",
      defaultHttpClient: {
        targetKey: "js",
        clientKey: "fetch",
      },
      spec: {
        url: "/doc",
      },
    }),
  );
}

import db from "@/db";
import type { JobWithLanguageReqs } from "@/db/schema";
import { getRedisClient } from "@/lib/redis/config";
import { logger } from "@/middlewares/pino-logger.middleware";
import cron from "node-cron";
import TelegramBot from "node-telegram-bot-api";
import { TELEGRAM_CONFIG } from "./config";
import {
  type VacancyWithMessage,
  formatVacancyList,
  formatVacancyMessage,
} from "./message-formatter";

export class TelegramBotService {
  private static bot: TelegramBot;
  private static redis = getRedisClient();

  private static getBot(): TelegramBot {
    if (!TelegramBotService.bot) {
      TelegramBotService.bot = new TelegramBot(TELEGRAM_CONFIG.token, {
        polling: true,
      });
    }
    return TelegramBotService.bot;
  }

  private static generateKey(key: string): string {
    return `telegram:messageId:${key}`;
  }

  private static async getMessageId(vacancyId: string): Promise<number | null> {
    const messageId = await TelegramBotService.redis.get(
      TelegramBotService.generateKey(vacancyId),
    );
    return messageId ? Number.parseInt(messageId, 10) : null;
  }

  private static setMessageId(
    vacancyId: string,
    messageId: number,
  ): Promise<"OK"> {
    return TelegramBotService.redis.set(
      TelegramBotService.generateKey(vacancyId),
      messageId.toString(),
      // "EX",
      // 60 * 60 * 24 * 30,
    );
  }

  public static startScheduledTasks() {
    cron.schedule(
      `${TELEGRAM_CONFIG.postSchedule.minute} ${TELEGRAM_CONFIG.postSchedule.hour} * * *`,
      () => {
        TelegramBotService.postActiveVacancies();
      },
    );

    cron.schedule(`0 */${TELEGRAM_CONFIG.messageUpdateInterval} * * *`, () => {
      TelegramBotService.updateVacancyStatuses();
    });

    cron.schedule("0 * * * *", () => {
      TelegramBotService.getBot().sendMessage(
        TELEGRAM_CONFIG.channelId,
        "Status: Healthy",
      );
    });
  }

  public static async sendVacancyMessage(vacancyId: string) {
    try {
      const vacancy = await db.query.jobVacancy.findFirst({
        where: {
          id: vacancyId,
        },
        with: {
          languageRequirements: true,
        },
      });

      if (!vacancy) {
        logger.error("Vacancy not found", { vacancyId });
        return;
      }

      const message = await formatVacancyMessage(vacancy);
      const sentMessage = await TelegramBotService.getBot().sendMessage(
        TELEGRAM_CONFIG.channelId,
        message,
        {
          parse_mode: "Markdown",
          disable_web_page_preview: true,
        },
      );

      await TelegramBotService.setMessageId(vacancy.id, sentMessage.message_id);

      return sentMessage;
    } catch (error) {
      logger.error("Failed to send vacancy message", { error, vacancyId });
      throw error;
    }
  }

  private static async postActiveVacancies() {
    try {
      const activeVacancies = await db.query.jobVacancy.findMany({
        where: {
          status: "published",
        },
        with: {
          languageRequirements: true,
        },
      });

      if (activeVacancies.length === 0) {
        return;
      }

      await Promise.all(
        activeVacancies.map(async (vacancy) => {
          const messageId = await TelegramBotService.getMessageId(vacancy.id);
          if (messageId) {
            (vacancy as VacancyWithMessage).messageId = messageId;
          }
        }),
      );

      const chunks = TelegramBotService.splitArray(activeVacancies, 10);

      for (const chunk of chunks) {
        const message = formatVacancyList(chunk);
        await TelegramBotService.getBot().sendMessage(
          TELEGRAM_CONFIG.channelId,
          message,
          {
            parse_mode: "Markdown",
            disable_web_page_preview: true,
          },
        );
        await TelegramBotService.delay(5000);
      }
    } catch (error) {
      logger.error("Failed to post active vacancies", { error });
    }
  }

  static async updateVacancyMessageById(vacancyId: string) {
    const vacancy = await db.query.jobVacancy.findFirst({
      where: {
        id: vacancyId,
      },
      with: {
        languageRequirements: true,
      },
    });

    if (!vacancy) {
      logger.error("Vacancy not found", { vacancyId });
      return;
    }

    await TelegramBotService.updateVacancyMessage({ vacancy });
  }

  static async updateVacancyMessage(by: {
    vacancy?: JobWithLanguageReqs;
    id?: string;
  }) {
    const { id } = by;
    let vacancy = by.vacancy;

    if (!vacancy) {
      if (!id) {
        throw new Error("Vacancy not found");
      }

      vacancy = await db.query.jobVacancy.findFirst({
        where: {
          id,
        },
        with: {
          languageRequirements: true,
        },
      });
      if (!vacancy) {
        logger.error("Vacancy not found", { vacancyId: id });
        return;
      }
      await TelegramBotService.updateVacancyMessage({ vacancy });
      return;
    }

    const messageId = await TelegramBotService.getMessageId(vacancy.id);
    if (!messageId) {
      logger.info("Message ID not found, creating new message", {
        vacancyId: vacancy.id,
      });
      return TelegramBotService.sendVacancyMessage(vacancy.id);
    }

    const message = await formatVacancyMessage(vacancy);
    await TelegramBotService.getBot().editMessageText(message, {
      chat_id: TELEGRAM_CONFIG.channelId,
      message_id: messageId,
      parse_mode: "Markdown",
    });
  }

  private static async updateVacancyStatuses() {
    try {
      const archivedVacancies = await db.query.jobVacancy.findMany({
        where: {
          status: "published",
          archivedAt: {
            isNotNull: true,
          },
        },
        with: {
          languageRequirements: true,
        },
      });

      for (const vacancy of archivedVacancies) {
        await TelegramBotService.updateVacancyMessage({ vacancy });
      }
    } catch (error) {
      logger.error("Failed to update vacancy statuses", { error });
    }
  }

  private static splitArray<T>(array: T[], size: number): T[][] {
    return array.reduce((acc, _, i) => {
      if (i % size === 0) {
        acc.push(array.slice(i, i + size));
      }
      return acc;
    }, [] as T[][]);
  }

  private static delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  public static async triggerActiveVacanciesPost() {
    await TelegramBotService.postActiveVacancies();
    return { success: true, message: "Active vacancies post triggered" };
  }

  public static async triggerStatusUpdates() {
    await TelegramBotService.updateVacancyStatuses();
    return { success: true, message: "Status updates triggered" };
  }

  public static async triggerHealthCheck() {
    await TelegramBotService.getBot().sendMessage(
      TELEGRAM_CONFIG.channelId,
      "Status: Healthy (Manual Trigger)",
    );
    return { success: true, message: "Health check triggered" };
  }
}

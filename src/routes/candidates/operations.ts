import type { NewCandidate } from "@/db/schema";

import db from "@/db";
import { candidate, jobApplication } from "@/db/schema";
import { and, count, eq, ilike } from "drizzle-orm";
import type { z } from "zod";
import { listCandidatesQueryDTO } from "./dtos";

const optionalParams = listCandidatesQueryDTO.optional();

export class CandidateOperations {
  static async findMany(params: z.infer<typeof optionalParams>) {
    const {
      page,
      limit,
      sortBy = "createdAt",
      sortDirection = "desc",
      search,
      status,
      source,
      responsibleManagerId,
    } = params ?? {};

    const [candidates, [{ total }]] = await Promise.all([
      db.query.candidate.findMany({
        limit,
        offset: limit && page ? (page - 1) * limit : undefined,
        where: {
          ...(search
            ? {
                fullName: {
                  ilike: `%${search}%`,
                },
              }
            : {}),
          ...(status ? { status } : {}),
          ...(source ? { source } : {}),
          ...(responsibleManagerId ? { responsibleManagerId } : {}),
        },
        with: {
          responsibleManager: {
            columns: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortDirection,
        },
      }),
      db
        .select({
          total: count(),
        })
        .from(candidate)
        .where(
          and(
            search ? ilike(candidate.fullName, `%${search}%`) : undefined,
            status ? eq(candidate.status, status) : undefined,
            source ? eq(candidate.source, source) : undefined,
            responsibleManagerId
              ? eq(candidate.responsibleManagerId, responsibleManagerId)
              : undefined,
          ),
        )
        .limit(1),
    ]);

    return {
      total,
      page,
      limit,
      pages: limit ? Math.ceil(total / limit) : undefined,
      items: candidates,
    };
  }

  static async findById(id: string) {
    return db.query.candidate.findFirst({
      where: { id },
    });
  }

  static async findByIdWithApplications(id: string) {
    return db.query.candidate.findFirst({
      where: { id },
      with: {
        responsibleManager: {
          columns: {
            id: true,
            name: true,
            image: true,
          },
        },
        jobApplications: {
          with: {
            jobVacancy: true,
          },
        },
      },
    });
  }

  static async findByContactInfo(phoneNumber: string) {
    const normalizedPhone = phoneNumber.replace(/[\s\-\(\)]/g, "");

    // Search for candidates that have this phone number in their phoneNumbers array
    const candidates = await db.query.candidate.findMany();

    return candidates.find((candidate) =>
      candidate.phoneNumbers.some(
        (phone) => phone.replace(/[\s\-\(\)]/g, "") === normalizedPhone,
      ),
    );
  }

  static async findOrCreate(candidateData: {
    fullName: string;
    phoneNumber: string;
    citizenship?: string;
  }) {
    const normalizedPhone = candidateData.phoneNumber.replace(
      /[\s\-\(\)]/g,
      "",
    );

    const existingCandidate =
      await CandidateOperations.findByContactInfo(normalizedPhone);

    if (existingCandidate) {
      // Add the new phone number if it's not already in the list
      const phoneNumbers = [...existingCandidate.phoneNumbers];
      if (
        !phoneNumbers.some(
          (phone) => phone.replace(/[\s\-\(\)]/g, "") === normalizedPhone,
        )
      ) {
        phoneNumbers.push(normalizedPhone);
      }

      const updatedCandidate = await CandidateOperations.update(
        existingCandidate.id,
        {
          fullName: candidateData.fullName,
          phoneNumbers,
          citizenship: candidateData.citizenship,
        },
      );
      return updatedCandidate;
    }

    return CandidateOperations.create({
      fullName: candidateData.fullName,
      phoneNumbers: [normalizedPhone],
      citizenship: candidateData.citizenship,
    });
  }

  static async create(data: NewCandidate) {
    const [newCandidate] = await db.insert(candidate).values(data).returning();
    return newCandidate;
  }

  static async update(id: string, updates: Partial<NewCandidate>) {
    const [updated] = await db
      .update(candidate)
      .set({
        ...updates,
        updatedAt: new Date(),
      })
      .where(eq(candidate.id, id))
      .returning();
    return updated;
  }

  static async delete(id: string) {
    const [deleted] = await db
      .delete(candidate)
      .where(eq(candidate.id, id))
      .returning();
    return !!deleted;
  }

  /**
   * Merge candidate B into candidate A
   * - Transfers all applications from B to A
   * - Merges phone numbers (avoiding duplicates)
   * - Deletes candidate B
   */
  static async mergeCandidates(
    targetCandidateId: string,
    sourceCandidateId: string,
  ) {
    const targetCandidate =
      await CandidateOperations.findById(targetCandidateId);
    const sourceCandidate =
      await CandidateOperations.findById(sourceCandidateId);

    if (!targetCandidate || !sourceCandidate) {
      throw new Error("One or both candidates not found");
    }

    if (targetCandidateId === sourceCandidateId) {
      throw new Error("Cannot merge candidate with itself");
    }

    return await db.transaction(async (tx) => {
      // 1. Transfer all applications from source to target
      await tx
        .update(jobApplication)
        .set({ candidateId: targetCandidateId })
        .where(eq(jobApplication.candidateId, sourceCandidateId));

      // 2. Merge phone numbers (avoiding duplicates)
      const mergedPhoneNumbers = [...targetCandidate.phoneNumbers];
      for (const phone of sourceCandidate.phoneNumbers) {
        const normalizedPhone = phone.replace(/[\s\-\(\)]/g, "");
        const exists = mergedPhoneNumbers.some(
          (existingPhone) =>
            existingPhone.replace(/[\s\-\(\)]/g, "") === normalizedPhone,
        );
        if (!exists) {
          mergedPhoneNumbers.push(phone);
        }
      }

      // 3. Update target candidate with merged phone numbers
      await tx
        .update(candidate)
        .set({
          phoneNumbers: mergedPhoneNumbers,
          updatedAt: new Date(),
        })
        .where(eq(candidate.id, targetCandidateId));

      // 4. Delete source candidate
      await tx.delete(candidate).where(eq(candidate.id, sourceCandidateId));

      // 5. Return updated target candidate
      return await tx.query.candidate.findFirst({
        where: {
          id: targetCandidateId,
        },
        with: {
          jobApplications: {
            with: {
              jobVacancy: true,
            },
          },
        },
      });
    });
  }
}

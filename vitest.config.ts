import path from "node:path";
import { defineConfig } from "vitest/config";

export default defineConfig({
  test: {
    sequence: {
      shuffle: false,
      concurrent: false,
      hooks: "stack",
    },
    poolOptions: {
      threads: {
        singleThread: true,
      },
    },
    globals: true,
    setupFiles: ["./src/test/setup.ts"],
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});

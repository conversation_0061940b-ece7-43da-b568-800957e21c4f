import { describeRoute } from "hono-openapi";
import * as HttpStatusCodes from "stoker/http-status-codes";
import z from "zod";

import { selectUserSchema } from "@/db/schema";
import { auth } from "@/lib/auth";
import { API_TAGS } from "@/lib/constants";
import { createRouter, jsonContent, validator } from "@/lib/helpers";
import { openApiResponses, responses } from "@/lib/responses";
import { listUsersDTO } from "@/lib/schemas";
import {
  paginationParamsSchema,
  patchUserSchema,
  userFilterSchema,
} from "@/lib/schemas";
import { authGuard } from "@/middlewares/auth.middleware";
import { permissionGuard } from "@/middlewares/permission.middleware";
import { listJobsQueryDTO, listJobsWithAuthorDTO } from "../job-vacancies/dtos";
import { JobVacancyOperations } from "../job-vacancies/operations";
import { createUserSchema, deleteAccountSchema } from "./dtos";
import { UsersOperations } from "./operations";

// Define allowed fields for the fields parameter
const allowedUserFields = z.enum([
  "id",
  "name",
  "description",
  "email",
  "image",
  "phoneNumber",
  "dateOfBirth",
  "gender",
  "role",
  "createdAt",
  "updatedAt",
]);

const router = createRouter()
  .use(authGuard)
  .get(
    "/",
    describeRoute({
      title: "List Users",
      description: "Get a paginated and filterable list of all users.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(listUsersDTO, "List of users"),
      },
    }),
    permissionGuard({ user: ["list"] }),
    validator(
      "query",
      paginationParamsSchema
        .merge(userFilterSchema)
        .extend({
          hasVacancies: z
            .boolean()
            .optional()
            .describe(
              "Filter to include users who have published job vacancies.",
            ),
          fields: z
            .string()
            .optional()
            .describe(
              "Comma-separated list of fields to return (e.g., id,name).",
            )
            .transform((val) => {
              if (!val) return undefined;
              const fieldArray = val
                .split(",")
                .map((s) => s.trim())
                .filter(Boolean);
              // Validate each field against the allowed enum
              for (const field of fieldArray) {
                allowedUserFields.parse(field); // This will throw if an invalid field is present
              }
              return fieldArray; // Return as string[]
            }),
        })
        .optional(),
    ),
    async (c) => {
      const query = c.req.valid("query") || {};
      const result = await UsersOperations.findMany(query);
      return c.json(result, HttpStatusCodes.OK);
    },
  )
  .get(
    "/profile",
    describeRoute({
      title: "Get Current User",
      description: "Get the current user's information.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          selectUserSchema,
          "Current user information",
        ),
        ...openApiResponses.unauthorized,
      },
    }),
    async (c) => {
      const user = await UsersOperations.findById(c.var.checkedUser.id);
      return c.json(user, HttpStatusCodes.OK);
    },
  )
  .post(
    "/",
    describeRoute({
      title: "Create User",
      description: "Create a new user. Requires user creation permission.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.CREATED]: jsonContent(
          selectUserSchema,
          "Created user",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    permissionGuard({ user: ["create"] }),
    validator(
      "json",
      createUserSchema.extend({ role: z.enum(["admin", "author", "editor"]) }),
    ),
    async (c) => {
      const data = c.req.valid("json");
      const result = await auth.api.createUser({
        body: {
          ...data,
        },
        headers: c.req.raw.headers,
      });

      return c.json(result.user, HttpStatusCodes.CREATED);
    },
  )
  .get(
    "/profile/job-vacancies",
    describeRoute({
      title: "List Own Job Vacancies",
      description: "List all job vacancies created by the current user.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          listJobsWithAuthorDTO,
          "List of user's job vacancies with author info",
        ),
        ...openApiResponses.unauthorized,
      },
    }),
    validator(
      "query",
      listJobsQueryDTO
        .extend({
          status: z.enum(["published", "archived", "draft"]).optional(),
        })
        .optional(),
    ),
    async (c) => {
      const query = c.req.valid("query");
      const userId = c.var.checkedUser.id;
      const vacancies = await JobVacancyOperations.findByAuthor(userId, query);
      return c.json(vacancies, HttpStatusCodes.OK);
    },
  )
  .get(
    "/:id",
    describeRoute({
      title: "Get User by ID",
      description: "Get a user by their ID.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          selectUserSchema.pick({
            id: true,
            name: true,
            description: true,
            email: true,
            image: true,
            phoneNumber: true,
            dateOfBirth: true,
            gender: true,
            role: true,
            createdAt: true,
            updatedAt: true,
          }),
          "User details",
        ),
        ...openApiResponses.notFound,
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
      },
    }),
    permissionGuard({ user: ["list"] }),
    validator("param", z.object({ id: z.string() })),
    async (c) => {
      const { id } = c.req.valid("param");
      const found = await UsersOperations.findById(id);
      if (!found) {
        return responses.notFound(c, `User with ID ${id}`);
      }
      return c.json(found, HttpStatusCodes.OK);
    },
  )
  .patch(
    "/profile",
    describeRoute({
      title: "Update Current User",
      description: "Update the current user's information.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(selectUserSchema, "Updated user"),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    validator("json", patchUserSchema),
    async (c) => {
      const updates = c.req.valid("json");
      const updated = await UsersOperations.patch(
        c.var.checkedUser.id,
        updates,
      );
      return c.json(updated, HttpStatusCodes.OK);
    },
  )
  .delete(
    "/profile",
    describeRoute({
      title: "Delete Current User Account",
      description:
        "Delete the current user's account with password verification. This will archive all published vacancies and delete draft vacancies.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          z.object({
            success: z.boolean(),
            deletionReason: z.string(),
            deletedAt: z.date(),
            message: z.string(),
          }),
          "Account deletion confirmation",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    validator("json", deleteAccountSchema),
    async (c) => {
      const { password, deletionReason } = c.req.valid("json");
      const userId = c.var.checkedUser.id;

      try {
        const result = await UsersOperations.deleteAccount(
          userId,
          password,
          deletionReason,
        );
        return c.json(
          {
            success: result.success,
            deletionReason: result.deletionReason,
            deletedAt: result.deletedAt,
            message:
              "Account has been successfully deleted and data anonymized.",
          },
          HttpStatusCodes.OK,
        );
      } catch (error) {
        return responses.unprocessableEntity(c, (error as Error).message);
      }
    },
  )
  .patch(
    "/:id",
    describeRoute({
      title: "Update User",
      description: "Update a user by their ID.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(selectUserSchema, "Updated user"),
        ...openApiResponses.notFound,
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.unprocessableEntity,
      },
    }),
    permissionGuard({ user: ["set-password"] }),
    validator("param", z.object({ id: z.string() })),
    validator(
      "json",
      patchUserSchema.extend({
        role: z.enum(["admin", "author", "editor"]).optional(),
      }),
    ),
    async (c) => {
      const { id } = c.req.valid("param");
      const updates = c.req.valid("json");
      const updated = await UsersOperations.patch(id, updates);
      if (!updated) {
        return responses.notFound(c, `User with ID ${id}`);
      }
      return c.json(updated, HttpStatusCodes.OK);
    },
  )
  .delete(
    "/:id",
    describeRoute({
      title: "Delete User",
      description: "Delete a user by their ID.",
      tags: [API_TAGS.USERS],
      responses: {
        [HttpStatusCodes.NO_CONTENT]: { description: "User deleted" },
        ...openApiResponses.notFound,
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
      },
    }),
    permissionGuard({ user: ["delete"] }),
    validator("param", z.object({ id: z.string() })),
    async (c) => {
      const { id } = c.req.valid("param");
      const deleted = await UsersOperations.delete(id);
      if (!deleted) {
        return responses.notFound(c, `User with ID ${id}`);
      }
      return c.body(null, HttpStatusCodes.NO_CONTENT);
    },
  );

export default router;
